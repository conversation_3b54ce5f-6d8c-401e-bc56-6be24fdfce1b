{
    // 使用clangd作为C/C++语言服务器
    "clangd.path": "clangd",
    "clangd.arguments": [
        "--background-index",
        "--clang-tidy",
        "--completion-style=detailed",
        "--function-arg-placeholders",
        "--header-insertion=iwyu",
        "--pch-storage=memory"
    ],
    
    // 禁用Microsoft C/C++插件的IntelliSense，使用clangd
    "C_Cpp.intelliSenseEngine": "disabled",
    "C_Cpp.autocomplete": "disabled",
    "C_Cpp.errorSquiggles": "disabled",
    
    // 文件关联
    "files.associations": {
        "*.h": "c",
        "*.hpp": "cpp",
        "*.cpp": "cpp",
        "*.cc": "cpp",
        "*.c": "c",
        // "*.proto": "protobuf"
    },
    
    // 排除一些不需要索引的目录
    "files.exclude": {
        "**/cmake-build-*": true,
        "**/build": true,
        "**/.git": true,
        // "**/0-bin": true,
        // "**/0-lib": true
    },
    
    // 搜索设置
    "search.exclude": {
        "**/cmake-build-*": true,
        "**/build": true,
        // "**/0-bin": true,
        // "**/0-lib": true
    }
} 