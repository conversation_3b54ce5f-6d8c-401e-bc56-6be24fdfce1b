{"version": "2.0.0", "tasks": [{"label": "CMake Configure", "type": "shell", "command": "cmake", "args": ["-B", "cmake-build-debug", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Configure CMake and generate compile_commands.json for clangd"}, {"label": "CMake Build", "type": "shell", "command": "cmake", "args": ["--build", "cmake-build-debug", "--config", "Debug", "--", "-j", "4"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "dependsOn": "CMake Configure", "detail": "Build the project using CMake"}, {"label": "Clean Build", "type": "shell", "command": "rm", "args": ["-rf", "cmake-build-debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Clean build directory"}]}