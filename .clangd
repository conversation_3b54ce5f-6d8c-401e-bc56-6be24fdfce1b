CompileFlags:
  Add: 
    - -std=c++14
    - -w  # 禁用所有警告，但保留错误
    - -Werror=return-type   # 但将"缺少返回值"作为错误处理
    - -Werror=uninitialized # 将"未初始化变量"作为错误处理
    - -DUSE_NVTX
    - -DCOMPILEDWITHC14
    - -DGIT_TAG=""
    - -DGIT_COMMITID=""
    - -I/usr/include/c++/11
    - -I/usr/include/x86_64-linux-gnu/c++/11
    - -I/usr/include/c++/11/backward
    - -I/usr/lib/gcc/x86_64-linux-gnu/11/include
    - -I/usr/local/include
    - -I/usr/include/x86_64-linux-gnu
    - -I/usr/include
    - -Ithird_party/cxxopts
    - -Isrc
    - -Iinclude
    - -I../xsproto_include
    - -I../faultlib/include
    - -I../xsmap/xsnml

Index:
  Background: Build
  
Diagnostics:
  ClangTidy:
    CheckOptions: {}
  UnusedIncludes: None
  
Completion:
  AllScopes: true
  
InlayHints:
  Enabled: false
  ParameterNames: false
  DeducedTypes: false

Hover:
  ShowAKA: true 