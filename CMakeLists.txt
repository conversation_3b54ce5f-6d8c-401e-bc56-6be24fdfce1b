project(data_auto_trigger)
cmake_minimum_required(VERSION 3.5)

# 根据平台架构设置构建类型
MESSAGE("Target architecture: " ${CMAKE_SYSTEM_PROCESSOR})
if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|AMD64|i386|i686")
    set(CMAKE_BUILD_TYPE Debug)
    MESSAGE("x86 platform detected, setting to Debug mode")
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
    set(CMAKE_BUILD_TYPE Release)
    MESSAGE("aarch64 platform detected, setting to Release mode")
else()
    set(CMAKE_BUILD_TYPE Release)
    MESSAGE("Unknown platform, defaulting to Release mode")
endif()
MESSAGE("Build type: " ${CMAKE_BUILD_TYPE})

## 设置编译标志
#set(CMAKE_C_FLAGS_DEBUG "-O2 -g")
#set(CMAKE_CXX_FLAGS_DEBUG "-O2 -g")
#set(CMAKE_C_FLAGS_RELEASE "-O3")
#set(CMAKE_CXX_FLAGS_RELEASE "-O3")

#USE_NVTX: NVIDIA Tools Extension (NVTX), 用于性能分析
list(APPEND CMAKE_C_FLAGS "-Wall -Wextra -DUSE_NVTX")
list(APPEND CMAKE_CXX_FLAGS "-Wall -Wextra -DUSE_NVTX")

# set complie config
include(CheckCXXCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-std=c++14" COMPILER_SUPPORTS_CXX14)
CHECK_CXX_COMPILER_FLAG("-std=c++11" COMPILER_SUPPORTS_CXX11)
CHECK_CXX_COMPILER_FLAG("-std=c++0x" COMPILER_SUPPORTS_CXX0X)
if(COMPILER_SUPPORTS_CXX14)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14")
    add_definitions(-DCOMPILEDWITHC14)
    message(STATUS "Using flag -std=c++14.")
elseif(COMPILER_SUPPORTS_CXX11)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
    add_definitions(-DCOMPILEDWITHC11)
    message(STATUS "Using flag -std=c++11.")
elseif(COMPILER_SUPPORTS_CXX0X)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x")
    add_definitions(-DCOMPILEDWITHC0X)
    message(STATUS "Using flag -std=c++0x.")
else()
    message(FATAL_ERROR "The compiler ${CMAKE_CXX_COMPILER} has no C++ support.")
endif()

execute_process(COMMAND bash "-c" "git describe --tags `git rev-list --tags --max-count=1`| tr '\n' ' '"
                OUTPUT_VARIABLE GIT_TAG)
message("=====git tag======")
message(STATUS ${GIT_TAG})
message("==================")
add_definitions(-DGIT_TAG="${GIT_TAG}")

execute_process(COMMAND bash "-c" "git rev-parse HEAD| tr '\n' ' '"
                OUTPUT_VARIABLE GIT_COMMITID)
message("=====git commitid======")
message(STATUS ${GIT_COMMITID})
message("==================")
add_definitions(-DGIT_COMMITID="${GIT_COMMITID}")

# get git datetime of last commit
execute_process(COMMAND bash "-c" "git log -1 --format=%cd --date=format:'%Y-%m-%d %H:%M:%S'| tr '\n' ' '"
                OUTPUT_VARIABLE GIT_COMMIT_DATETIME)
message("=====git commit datetime======")
message(STATUS ${GIT_COMMIT_DATETIME})
message("==================")
add_definitions(-DGIT_COMMIT_DATETIME="${GIT_COMMIT_DATETIME}")

# Detect OS version and architecture
execute_process(COMMAND bash "-c" "lsb_release -rs | tr -d '\\n'"
                OUTPUT_VARIABLE LSB_RELEASE_VERSION
                ERROR_QUIET)
if(NOT LSB_RELEASE_VERSION)
    execute_process(COMMAND bash "-c" "cat /etc/os-release | grep VERSION_ID | cut -d'=' -f2 | tr -d '\"\\n'"
                    OUTPUT_VARIABLE LSB_RELEASE_VERSION
                    ERROR_QUIET)
endif()

execute_process(COMMAND bash "-c" "uname -m | tr -d '\\n'"
                OUTPUT_VARIABLE SYSTEM_ARCHITECTURE)

message("=====OS Info======")
message(STATUS "LSB_RELEASE_VERSION: ${LSB_RELEASE_VERSION}")
message(STATUS "SYSTEM_ARCHITECTURE: ${SYSTEM_ARCHITECTURE}")
message("==================")

find_package(OpenCV REQUIRED)

set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/0-bin)
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/0-lib)

include_directories(
    ${OpenCV_INCLUDE_DIRS}
    ${PROJECT_SOURCE_DIR}/src
    ${PROJECT_SOURCE_DIR}/include
    ${PROJECT_SOURCE_DIR}/res_cache
    ${PROJECT_SOURCE_DIR}/../xsproto_include
    ${PROJECT_SOURCE_DIR}/../faultlib/include
    ${PROJECT_SOURCE_DIR}/../xsmap/xsnml
    ${PROJECT_SOURCE_DIR}/third_party
    ${PROJECT_SOURCE_DIR}/third_party
    )

link_directories(
    ${OpenCV_LIBRARY_DIRS}
    ${LIBRARY_OUTPUT_PATH}
    /usr/local/lib
    )
     
add_subdirectory(src/public)
add_subdirectory(res_cache)
file(GLOB SRCS "src/*.cpp" "src/*.cc" "src/*.c" "src/*.hpp" "src/*.h")
add_executable(${PROJECT_NAME} ${SRCS})

target_link_libraries(
        ${PROJECT_NAME}
        ${OpenCV_LIBS}
        public
        res_cache
        pthread
        protobuf
        rcs
        fastrtps
        fastcdr
        xscom
        #proto
        -L${PROJECT_SOURCE_DIR}/../../lib/${LSB_RELEASE_VERSION}_${SYSTEM_ARCHITECTURE} libxsproto.a
        # RCS
        -L${PROJECT_SOURCE_DIR}/../../lib/${LSB_RELEASE_VERSION}_${SYSTEM_ARCHITECTURE} libxsnml.a
)

# 定义process_name为project_name, 以便在C++中使用
add_definitions(-DPROCESS_NAME="${PROJECT_NAME}")