#include "unified_comm.h"
#include <cerrno> // for errno
#include <cstring> // for strerror
#include <stdexcept> // for runtime_error

UnifiedComm::UnifiedComm() {
    // InitQueue();
    char *ugv_com = std::getenv("XS_UGV_COM");
    if (ugv_com == nullptr) {
        std::cout << "XS_UGV_COM is not set, use RCS as default" << std::endl;
        // std::exit(EXIT_FAILURE);
    }

    if (strcmp(ugv_com, "XSCOM") == 0) {
        ugv_com_ = CommType_XSCOM;
        InitXscom();
        // LifeSignalHandler::Init();
    } else {
        ugv_com_ = CommType_RCS;
        InitRcs();
    }
    std_thread_ = std::thread(&UnifiedComm::GetBufferData, this);
}

UnifiedComm::~UnifiedComm() {
    if (std_thread_.joinable())
        std_thread_.join();
    if (ugv_com_ == CommType_RCS) {
        //delete lp_rcs_channel_;
        delete hdmap_rcs_channel_;
        delete tl_rcs_channel_;
        delete perception_rcs_channel_;
        nml_cleanup();
    }
}

// void UnifiedComm::InitQueue() {
//     lp_deque_.clear();
//     hdmap_deque_.clear();
//     trafficlight_deque.clear();
//     perception_deque.clear();
// }

void UnifiedComm::InitRcs() {
    nml_start();

    std::string program_path = GetProgramPath();
    std::string nml_config_file = program_path + "/UGVAuto.nml";

    hdmap_rcs_channel_ = new NML(Buffer_2mb_MsgFormat, CHANNEL_LocalHDMap, NODE_NAME, nml_config_file.c_str());
    if (hdmap_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        LogError("[RCS] no master error: LocalHDMap");
        hdmap_rcs_channel_->delete_channel();
        hdmap_rcs_channel_ =
                new NML(Buffer_2mb_MsgFormat, CHANNEL_LocalHDMap, NODE_NAME, nml_config_file.c_str(), 0, 1);
        LogInfo("[RCS] set as master: LocalHDMap");
    }

    tl_rcs_channel_ = new NML(Buffer_10kb_MsgFormat, CHANNEL_TrafficLightInfo, NODE_NAME, nml_config_file.c_str());
    if (tl_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        LogError("[RCS] no master error: TrafficLightInfo");
        tl_rcs_channel_->delete_channel();
        tl_rcs_channel_ =
                new NML(Buffer_10kb_MsgFormat, CHANNEL_TrafficLightInfo, NODE_NAME, nml_config_file.c_str(), 0, 1);
        LogInfo("[RCS] set as master: TrafficLightInfo");
    }

    perception_rcs_channel_ =
            new NML(Buffer_1mb_MsgFormat, CHANNEL_LateFusionObjectInfo, NODE_NAME, nml_config_file.c_str());
    if (perception_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        LogError("[RCS] no master error: LateFusionObjectInfo");
        perception_rcs_channel_->delete_channel();
        perception_rcs_channel_ =
                new NML(Buffer_1mb_MsgFormat, CHANNEL_LateFusionObjectInfo, NODE_NAME, nml_config_file.c_str(), 0, 1);
        LogInfo("[RCS] set as master: LateFusionObjectInfo");
    }

    // init data cache
    rcs_hdmap_cache_ = std::make_shared<LocalHdmapMsg>();
    rcs_tl_cache_ = std::make_shared<TrafficLightMsg>();
    rcs_fused_cache_ = std::make_shared<PerceptionMsg>();

    LogInfo("[RCS] init complete.");
}

void UnifiedComm::InitXscom() {
    xscom::Init(NODE_NAME);

    crt_node_ = xscom::CreateNode(NODE_NAME);
    if (crt_node_ == nullptr) {
        std::cerr << "[CyberRT] failed to init Cyber Node: " << NODE_NAME << std::endl;
        // std::exit(EXIT_FAILURE);
    }

    crt_perception_reader_ = crt_node_->CreateReader<PerceptionMsg>(CHANNEL_LateFusionObjectInfo, nullptr);
    if (crt_perception_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_LateFusionObjectInfo << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_LateFusionObjectInfo);
        // std::exit(EXIT_FAILURE);
    }

    crt_hdmap_reader_ = crt_node_->CreateReader<LocalHdmapMsg>(CHANNEL_LocalHDMap, nullptr);
    if (crt_hdmap_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_LocalHDMap << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_LocalHDMap);
        // std::exit(EXIT_FAILURE);
    }

    crt_trafficlight_reader_ = crt_node_->CreateReader<TrafficLightMsg>(CHANNEL_TrafficLightInfo, nullptr);
    if (crt_trafficlight_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_TrafficLightInfo << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_TrafficLightInfo);
        // std::exit(EXIT_FAILURE);
    }

    LogInfo("[XSCOM] init complete.");
}

void UnifiedComm::GetBufferData() {
    while (!LifeSignalHandler::IsShutdown()) {
        // 获取buffer数据
        if (ugv_com_ == CommType_XSCOM) {
            CrtReadFusedInfo();
            CrtReadLocalHdmap();
            CrtReadTrafficLight();
        } else {
            // ReadLocalPose();
            RcsReadLocalHdmap();
            RcsReadTrafficLight();
            RcsReadFusedInfo();
        }
        usleep(100000);
    }
}

// bool UnifiedComm::ReadLocalPose() {
//     std::lock_guard<std::mutex> lock(lp_mutex_);
//     if (BUFFER_10KB_MSG_TYPE == lp_rcs_channel_->read()) {
//         BUFFER_10KB_MSG *lp_data = (BUFFER_10KB_MSG *) lp_rcs_channel_->get_address();
//         LocalPoseMsg temp_lp_data;
//         bool flag = temp_lp_data.ParseFromArray(lp_data->data, lp_data->len);
//         if (flag) {
//             if (lp_deque_.size() < 100) {
//                 //                m_lp_mutex.lock();
//                 lp_deque_.push_back(temp_lp_data);
//                 //                m_lp_mutex.unlock();
//                 return true;
//             } else {
//                 //                m_lp_mutex.lock();
//                 lp_deque_.pop_front();
//                 lp_deque_.push_back(temp_lp_data);
//                 //                m_lp_mutex.unlock();
//                 return true;
//             }
//         } else {
//             return false;
//         }
//     } else {
//         return false;
//     }
// }

bool UnifiedComm::RcsReadLocalHdmap() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    if (BUFFER_2MB_MSG_TYPE == hdmap_rcs_channel_->read()) {
        BUFFER_2MB_MSG *hdmap_data = (BUFFER_2MB_MSG *) hdmap_rcs_channel_->get_address();
        bool data_ok = rcs_hdmap_cache_->ParseFromArray(hdmap_data->data, hdmap_data->len);
        if (data_ok) {
            hdmap_deque_.push_back(*rcs_hdmap_cache_);
            if (hdmap_deque_.size() > kHdmapMaxQueueSize) {
                hdmap_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadLocalHdmap() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    crt_hdmap_reader_->Observe();
    std::shared_ptr<LocalHdmapMsg> hdmap_msg = crt_hdmap_reader_->GetLatestObserved();
    if (nullptr != hdmap_msg) {
        hdmap_deque_.push_back(*hdmap_msg);
        if (hdmap_deque_.size() > kHdmapMaxQueueSize) {
            hdmap_deque_.pop_front();
        }
        return true;
    }
    return false;
}

bool UnifiedComm::RcsReadFusedInfo() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    if (BUFFER_1MB_MSG_TYPE == perception_rcs_channel_->read()) {
        BUFFER_1MB_MSG *fused_data = (BUFFER_1MB_MSG *) perception_rcs_channel_->get_address();
        bool data_ok = rcs_fused_cache_->ParseFromArray(fused_data->data, fused_data->len);
        if (data_ok) {
            perception_deque_.push_back(*rcs_fused_cache_);
            if (perception_deque_.size() > kPerceptionMaxQueueSize) {
                perception_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadFusedInfo() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    crt_perception_reader_->Observe();
    std::shared_ptr<PerceptionMsg> fused_msg = crt_perception_reader_->GetLatestObserved();
    if (nullptr != fused_msg) {
        perception_deque_.push_back(*fused_msg);
        if (perception_deque_.size() > kPerceptionMaxQueueSize) {
            perception_deque_.pop_front();
        }
        return true;
    }
    return false;
}

bool UnifiedComm::RcsReadTrafficLight() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    if (BUFFER_10KB_MSG_TYPE == tl_rcs_channel_->read()) {
        BUFFER_10KB_MSG *trafficlight_data = (BUFFER_10KB_MSG *) tl_rcs_channel_->get_address();
        bool data_ok = rcs_tl_cache_->ParseFromArray(trafficlight_data->data, trafficlight_data->len);
        if (data_ok) {
            trafficlight_deque_.push_back(*rcs_tl_cache_);
            if (trafficlight_deque_.size() > kTrafficLightMaxQueueSize) {
                trafficlight_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadTrafficLight() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    crt_trafficlight_reader_->Observe();
    std::shared_ptr<TrafficLightMsg> trafficlight_msg = crt_trafficlight_reader_->GetLatestObserved();
    if (nullptr != trafficlight_msg) {
        trafficlight_deque_.push_back(*trafficlight_msg);
        if (trafficlight_deque_.size() > kTrafficLightMaxQueueSize) {
            trafficlight_deque_.pop_front();
        }
        return true;
    }
    return false;
}

std::deque<PerceptionMsg> UnifiedComm::GetPerceptionQueue() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    return perception_deque_;
}

std::deque<LocalHdmapMsg> UnifiedComm::GetHDmapQueue() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    return hdmap_deque_;
}

std::deque<TrafficLightMsg> UnifiedComm::GetTrafficLightQueue() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    return trafficlight_deque_;
}

void UnifiedComm::SendTask(TaskParam param) {
    xsdaq::Client client;
    xsdaq::msg::Task task;
    task.setDuration(param.duration); // 设置采集时间，秒
    task.setTaskName(param.task_name); // 设置采集名称
    for (int i = 0; i < param.data_sources.size(); i++) {
        task.addDataSource(param.data_sources[i]); // 添加采集目标
    }
    task.setTaskSource(param.task_source); // 添加采集任务发起来源
    task.addTag(param.collect_scene, param.collect_reason, TAG_Source_Daq); // 添加数据标签或采集原因

    xsdaq::ErrorCode err_code = client.start(task, [&](const xsdaq::msg::TaskError &err_msg) {
        std::cout << "On Task Resp:" << task.taskId() << std::endl;
    });
}

std::string UnifiedComm::GetProgramPath() {
    // 通过/proc/self/exe获取程序路径
    constexpr size_t MAX_PATH = 1024 * 2;
    std::string exe_path(MAX_PATH, '\0');

    ssize_t len = readlink("/proc/self/exe", &exe_path[0], MAX_PATH - 1);
    if (len == -1) {
        throw std::runtime_error("Failed to read executable path: " + std::string(strerror(errno)));
    }

    exe_path.resize(len); // 实际长度

    // 提取目录路径（移除文件名）
    size_t last_slash = exe_path.find_last_of('/');
    if (last_slash == std::string::npos) {
        throw std::runtime_error("Invalid executable path format: " + exe_path);
    }

    return exe_path.substr(0, last_slash);
}
