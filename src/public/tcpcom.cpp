#include "tcpcom.h"


CSocketTCP::CSocketTCP()
{
    m_socket_fd = -1;
    m_connect_flag = false;
}


CSocketTCP::~CSocketTCP()
{
    TcpRelease();
}


int CSocketTCP::TcpInit(std::string ip, int dst_port)
{
    // fd释放
    TcpRelease();
    // 申请socket_fd
    m_socket_fd = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if(m_socket_fd < 0)
    {
        std::cerr << "----------Socket creation error!" << std::endl;
        return -1;
    }
    // 设置禁用Nagle，接收timeout
    int enable = 1;
    setsockopt(m_socket_fd, IPPROTO_TCP, TCP_NODELAY, &enable, sizeof(enable));
    struct timeval timeout;
    timeout.tv_sec = 1;
    timeout.tv_usec = 0;
    setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVTIMEO, (const void*)&timeout, sizeof(timeout));
    // 连接远端
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_port = htons(dst_port);
    m_socket_addr.sin_addr.s_addr = inet_addr(ip.c_str());
    if(connect(m_socket_fd, (struct sockaddr*)&m_socket_addr, sizeof(m_socket_addr)) < 0)
    {
        TcpRelease();
        std::cerr << "----------Socket connect error!" << std::endl;
        return -1;
    }
    m_connect_flag = true;
    // 成功连接
    return 0;
}


int CSocketTCP::TcpRecv(char *buf)
{
    int recv_bytes = -1;
    recv_bytes = recv(m_socket_fd, buf, MAX_DATA_LENGTH, 0);
    if (recv_bytes == -1)
    {
        m_connect_flag = false;
        std::cerr << "----------Socket recv error!" << std::endl;
    }

    return recv_bytes;
}


int CSocketTCP::TcpSend(const char *buf, int len)
{
    int send_bytes = -1;
    send_bytes = send(m_socket_fd, buf, len, 0);
    if(send_bytes == -1)
    {
        m_connect_flag = false;
        std::cerr << "----------Socket send error!" << std::endl;
    }

    return send_bytes;
}


void CSocketTCP::TcpRelease()
{
    if(m_socket_fd >= 0)
    {
        close(m_socket_fd);
        m_socket_fd = -1;
        m_connect_flag = false;
    }
}
