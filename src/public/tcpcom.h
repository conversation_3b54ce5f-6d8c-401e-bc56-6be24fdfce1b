#ifndef	 __TCPCOM_H__
#define  __TCPCOM_H__

#include <iostream>
#include <chrono>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#define MAX_DATA_LENGTH 1500


class CSocketTCP
{
public:
    CSocketTCP();
    ~CSocketTCP();
    // TCP接收初始化
    int TcpInit(std::string ip, int dst_port);
    // TCP接收
    int TcpRecv(char *buf);
    // TCP发送
    int TcpSend(const char *buf, int len);
    // TCP重连接
    void TcpReconnect();
    // TCP释放
    void TcpRelease();
public:
    bool m_connect_flag;
private:
    int m_socket_fd;
    struct sockaddr_in m_socket_addr;
};
#endif
