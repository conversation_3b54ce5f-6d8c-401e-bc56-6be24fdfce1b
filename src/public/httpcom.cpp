#include "httpcom.h"


CHttpCom::CHttpCom()
{

}


CHttpCom::~CHttpCom()
{

}

int CHttpCom::HttpGet(std::string ip, int dst_port, const char *request, char *response,
                      char *content, int *status_code)
{
    TcpInit(ip, dst_port);
    // 发http请求
    while (!xsproto::common::LifeSignalHandler::IsShutdown())
    {
        if (m_connect_flag)
        {
            if (-1 != TcpSend(request, strlen(request)))
            {
                break;
            }
        }
        else
        {
            TcpInit(ip, dst_port);
        }
    }

    // 如果不获取返回值则结束
    if (response == NULL)
    {
        TcpRelease();
        return 0;
    }

    // 获取http请求返回值
    while (!xsproto::common::LifeSignalHandler::IsShutdown())
    {
        if (m_connect_flag)
        {
            int recv_bytes = TcpRecv(response);
            if (-1 != recv_bytes)
            {
                std::cout << "recv_bytes: " << recv_bytes << response << std::endl;
                // 获取content
                if (content != NULL)
                {
                    char *content_length = strstr(response, "Content-Length: ");

                    for (int i = 0; i < 30; ++i)
                    {
                        if (content_length[i] == '\r' || content_length[i] == '\n')
                        {
                            content_length[i] = '\0';
                            break;
                        }
                    }
                    std::string str = "";
                    for (int i = 0; content_length[i] != '\0'; i++)
                    {
                        if (content_length[i] >= '0' && content_length[i] <= '9')
                        {
                            str = str + content_length[i];
                        }
                    }

                    int content_size = atoi(str.c_str());
                    strcpy(content, response + (recv_bytes - content_size));
                }

                // 获取http响应状态码
                // todo


                break;
            }
        }
        else
        {
            TcpInit(ip, dst_port);
        }
    }

    TcpRelease();
    return 0;
}
