#ifndef FAULTCONTROL_H
#define FAULTCONTROL_H
#include <string>
#include "module_interface.h"
#include "trace_interface.h"
#include "common/common.h"


class FaultControl
{
public:
    FaultControl(int fault_code, int fault_level);
    FaultControl(int fault_code, int fault_level, std::string fault_msg);
    void SetFault();
    void SetFault(std::string msg);
    void ClearFault();
    void ClearFault(std::string msg);
private:
    double m_last_set_time;     //故障码建立时间
    int m_fault_code;         //故障码
    int m_fault_level;        //0:Fault, 1:Error, 2:Warn
    int m_status;             //0:无错误，1:错误存在
    std::string m_fault_msg;  //故障码说明
};

#endif // FAULTCONTROL_H
