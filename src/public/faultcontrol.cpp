#include "faultcontrol.h"


FaultControl::FaultControl(int fault_code, int fault_level)
{
    m_status = 0;
    m_fault_code = fault_code;
    m_fault_level = fault_level;
    m_fault_msg = "";
    std::string clear_msg = "Init clear fault.";
    if(m_fault_level == 0)
        TraceFaultClear(m_fault_code, "%s", clear_msg.c_str());
    else if(m_fault_level == 1)
        TraceErrorClear(m_fault_code, "%s", clear_msg.c_str());
    else
        TraceWarnClear(m_fault_code, "%s", clear_msg.c_str());
}


FaultControl::FaultControl(int fault_code, int fault_level, std::string fault_msg)
{
    m_status = 0;
    m_fault_code = fault_code;
    m_fault_level = fault_level;
    m_fault_msg = fault_msg + " ";
    std::string clear_msg = "Init clear fault.";
    if(m_fault_level == 0)
        TraceFaultClear(m_fault_code, "%s", clear_msg.c_str());
    else if(m_fault_level == 1)
        TraceErrorClear(m_fault_code, "%s", clear_msg.c_str());
    else
        TraceWarnClear(m_fault_code, "%s", clear_msg.c_str());
}


void FaultControl::SetFault()
{
    if(m_status == 0)
    {
        std::string clear_msg = "Alive----" + m_fault_msg;
        if(m_fault_level == 0)
            TraceFault(m_fault_code, "%s", clear_msg.c_str());
        else if(m_fault_level == 1)
            TraceError(m_fault_code, "%s", clear_msg.c_str());
        else
            TraceWarn(m_fault_code, "%s", clear_msg.c_str());
        m_status = 1;
        m_last_set_time = xsproto::common::getCurrentMillSecond() / 1000;
        std::cout << clear_msg << std::endl;
        clear_msg = clear_msg + "\n";
        LogError("%s", clear_msg.c_str());
    }
    else
    {
        double curr_time = xsproto::common::getCurrentMillSecond() / 1000;
        if(curr_time < m_last_set_time || curr_time - m_last_set_time > 10.0)
        {
            std::string clear_msg = "Alive----" + m_fault_msg;
            if(m_fault_level == 0)
                TraceFault(m_fault_code, "%s", clear_msg.c_str());
            else if(m_fault_level == 1)
                TraceError(m_fault_code, "%s", clear_msg.c_str());
            else
                TraceWarn(m_fault_code, "%s", clear_msg.c_str());
            m_status = 1;
            m_last_set_time = curr_time;
        }
    }
}


void FaultControl::SetFault(std::string msg)
{
    if(m_status == 0)
    {
        std::string clear_msg = "Alive----" + m_fault_msg + msg;
        if(m_fault_level == 0)
            TraceFault(m_fault_code, "%s", clear_msg.c_str());
        else if(m_fault_level == 1)
            TraceError(m_fault_code, "%s", clear_msg.c_str());
        else
            TraceWarn(m_fault_code, "%s", clear_msg.c_str());
        m_status = 1;
        m_last_set_time = xsproto::common::getCurrentMillSecond() / 1000;
        std::cout << clear_msg << std::endl;
        clear_msg = clear_msg + "\n";
        LogError("%s", clear_msg.c_str());
    }
    else
    {
        double curr_time = xsproto::common::getCurrentMillSecond() / 1000;
        if(curr_time < m_last_set_time || curr_time - m_last_set_time > 10.0)
        {
            std::string clear_msg = "Alive----" + m_fault_msg + msg;
            if(m_fault_level == 0)
                TraceFault(m_fault_code, "%s", clear_msg.c_str());
            else if(m_fault_level == 1)
                TraceError(m_fault_code, "%s", clear_msg.c_str());
            else
                TraceWarn(m_fault_code, "%s", clear_msg.c_str());
            m_status = 1;
            m_last_set_time = curr_time;
        }
    }
}


void FaultControl::ClearFault()
{
    if(m_status == 1)
    {
        std::string clear_msg = "Clear----" + m_fault_msg;
        if(m_fault_level == 0)
            TraceFaultClear(m_fault_code, "%s", clear_msg.c_str());
        else if(m_fault_level == 1)
            TraceErrorClear(m_fault_code, "%s", clear_msg.c_str());
        else
            TraceWarnClear(m_fault_code, "%s", clear_msg.c_str());
        m_status = 0;
        std::cout << clear_msg << std::endl;
        clear_msg = clear_msg + "\n";
        LogError("%s", clear_msg.c_str());
    }
}


void FaultControl::ClearFault(std::string msg)
{
    if(m_status == 1)
    {
        std::string clear_msg = "Clear----" + m_fault_msg + msg;
        if(m_fault_level == 0)
            TraceFaultClear(m_fault_code, "%s", clear_msg.c_str());
        else if(m_fault_level == 1)
            TraceErrorClear(m_fault_code, "%s", clear_msg.c_str());
        else
            TraceWarnClear(m_fault_code, "%s", clear_msg.c_str());
        m_status = 0;
        std::cout << clear_msg << std::endl;
        clear_msg = clear_msg + "\n";
        LogError("%s", clear_msg.c_str());
    }
}
