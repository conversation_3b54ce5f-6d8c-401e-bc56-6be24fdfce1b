#ifndef PROC_PROCESS_H_
#define PROC_PROCESS_H_

#include <unistd.h>

#include <functional>
#include <iostream>
#include <thread>

#include "unified_comm.h"

// RCS
#include "buffer_10kb.hh"
#include "buffer_2mb.hh"
// XSCOM
#include <xscom/xscom.h>
// XSProto
#include "base/global_pose.pb.h"
#include "base/local_pose.pb.h"
#include "channeldefine.h"
#include "common/common.h"
#include "nodedefine.h"
#include "public/CJsonObject.hpp"
// 触发器参数
#include "trigger_param.h"

class ProcProcess {
    // 触发器参数列表（存储所有启用的触发器）
    TriggerParamList all_triggers_;

public:
    ProcProcess();

    ~ProcProcess();

    void Process();

    // void Trigger(bool (TrafficLightTrigger::*func)(UnifiedComm *, TrafficCollectParam), TrafficLightTrigger *obj,
    //              TrafficCollectParam param);

    // void Trigger(bool (PerceptionDataTrigger::*func)(UnifiedComm *, PerceptionTriggerParam),
    //              PerceptionDataTrigger *obj, PerceptionTriggerParam param);

private:
    std::unique_ptr<UnifiedComm> unified_comm_{nullptr};

    // std::unique_ptr<PerceptionDataTrigger> m_perception_trigger;

    // std::unique_ptr<TrafficLightTrigger> m_traffic_trigger;

    // // 触发参数
    // TrafficCollectParam m_stopline_distance;
    // TrafficCollectParam m_Traffic_change;
    // PerceptionTriggerParam perception_param;
};

#endif
