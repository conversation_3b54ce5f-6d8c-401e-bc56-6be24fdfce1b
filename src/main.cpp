/*
 * main.cpp: 主流程入口

 * @Author: lhw
 * @Date: 2025/07/22
 * @LastEditTime: 2025/07/22
 * @Description: 主函数
 */

#include <iostream>
#include <string>
#include <memory>
#include "cxxopts/cxxopts.hpp"
#include "color_print.hpp"
#include "proc_process.h"
#include "trigger_param.h"

// print version information
void display_version_info();

int parser_options(int argc, char **argv);

void set_log_info(std::string folder_name);

int main(int argc, char **argv) {
    if (parser_options(argc, argv) != EXIT_SUCCESS) {
        return EXIT_SUCCESS;
    }

    set_log_info(PROCESS_NAME);

    // do not swap the order!
    LifeSignalHandler::Init();
    auto handle = std::make_unique<ProcProcess>();

    while (!LifeSignalHandler::IsShutdown()) {
        handle->Process();
    }

    //handle.reset();
    return 0;
}

// Placeholder function to print version information
void display_version_info() {
    // get the program name, and remove the path
    char path[PATH_MAX];
    std::string program_name(PROCESS_NAME);

    // get the path of current process from /proc/self/exe
    ssize_t count = readlink("/proc/self/exe", path, PATH_MAX);

    if (count != -1) {
        path[count] = '\0';

        // extract only the filename from the path
        std::string fullPath(path);
        size_t lastSlash = fullPath.find_last_of("/");

        // return empty string if no slash found, otherwise return the filename
        program_name = (lastSlash == std::string::npos) ? "" : fullPath.substr(lastSlash + 1);
    }

    std::string git_hash_str = GIT_COMMITID;
    // std::string git_date_str = REPO_DATE;
    std::cout << std::endl;
    std::cout << COUT_BOLDGREEN << "==========================================================" << COUT_RESET
            << std::endl;
    std::cout << COUT_BOLDGREEN << "   program:       " << COUT_BOLDRED << program_name << COUT_RESET << std::endl;
    std::cout << COUT_BOLDGREEN << "   version:       " << COUT_BOLDCYAN << GIT_TAG << COUT_RESET << std::endl;
    std::cout << COUT_BOLDGREEN << "   git hash:      " << COUT_BOLDCYAN << git_hash_str.substr(0, 8) << COUT_RESET
            << git_hash_str.substr(8) << std::endl;
    std::cout << COUT_BOLDGREEN << "   git date:      " << COUT_BOLDCYAN << GIT_COMMIT_DATETIME << COUT_RESET
            << std::endl;
    std::cout << COUT_BOLDGREEN << "   compile on:    " << COUT_BOLDCYAN << __DATE__ << " " << __TIME__ << COUT_RESET
            << std::endl;
    std::cout << COUT_BOLDGREEN << "   config file:   " << COUT_BOLDCYAN << param::_CONFIG_FILE_ << COUT_RESET
            << std::endl;
    // module id and sub id
    //  std::cout << COUT_BOLDGREEN << "   module type:   " << COUT_BOLDCYAN <<
    //  "0x" << std::hex << Params::instance()->cfg.fault_module_type << std::dec
    //  << " (" << Params::instance()->cfg.fault_module_type << ")" << COUT_RESET
    //  << std::endl; std::cout << COUT_BOLDGREEN << "   module id:     " <<
    //  COUT_BOLDCYAN << "0x" << std::hex <<
    //  Params::instance()->cfg.fault_module_id << std::dec << " (" <<
    //  Params::instance()->cfg.fault_module_id << ")" << COUT_RESET << std::endl;
    //  std::cout << COUT_BOLDGREEN << "   sub id:        " << COUT_BOLDCYAN <<
    //  "0x" << std::hex << Params::instance()->cfg.fault_sub_id << std::dec << "
    //  (" << Params::instance()->cfg.fault_sub_id << ")" << COUT_RESET <<
    //  std::endl;
    std::cout << COUT_BOLDGREEN << "==========================================================" << COUT_RESET
            << std::endl;
    std::cout << std::endl;
}

int parser_options(int argc, char **argv) {
    display_version_info();
    cxxopts::Options options(std::string(PROCESS_NAME),
                             std::string("A event-based trigger application for automatically collect data"));
    options.add_options()("h,help", "print help")("v,version", "print version");

    cxxopts::ParseResult result;
    try {
        result = options.parse(argc, argv);
    } catch (const cxxopts::exceptions::exception &e) {
        std::cerr << "Argument error: " << e.what() << std::endl;
        std::cerr << options.help() << std::endl;
        return EXIT_FAILURE;
    }

    // Handle help and version flags
    if (result.count("help")) {
        std::cout << options.help() << std::endl;
        return EXIT_FAILURE;
    }
    if (result.count("version")) {
        return EXIT_FAILURE;
    }

    return EXIT_SUCCESS;
}

void set_log_info(std::string folder_name) {
    // 定义log
    std::string log_path;
    char *env_ptr = std::getenv("XS_LOG_PATH");
    if (env_ptr == NULL) {
        std::cerr << "ERROR: XS_LOG_PATH has not been set!" << std::endl;
        //        TraceFault(0x0002, "Could not get env XS_LOG_PATH.");
        //        TraceCheckFailed();
        sleep(2);
        std::exit(EXIT_FAILURE);
    } else {
        log_path = env_ptr;
    }

    SINGLETON_LOG.setTraceLog(folder_name, log_path, 2, 0, 3, 0);
    std::stringstream ss;
    ss << "start execution: " << folder_name << ", process id: " << int(getpid());

    std::string str_text = ss.str();
    LogInfo("%s", str_text.c_str());
    std::cout << str_text << std::endl;
}
