#include "proc_process.h"
#include "nlohmann/json.hpp"

ProcProcess::ProcProcess() {
    // 加载所有启用的触发器配置（只读取enabled=true的项）
    all_triggers_ = TriggerParamFactory::LoadConfig();
    // 打印所有触发器的详细配置信息
    TriggerParamFactory::PrintAllTriggers(all_triggers_);

    std::cout << "Initialized ProcProcess with " << all_triggers_.size() << " enabled triggers" << std::endl;

    unified_comm_ = std::make_unique<UnifiedComm>();
    // m_traffic_trigger = std::make_unique<TrafficLightTrigger>();
    // m_perception_trigger = std::make_unique<PerceptionDataTrigger>();
}

ProcProcess::~ProcProcess() {
    // m_data_reader.reset();
    // m_traffic_trigger.reset();
    // m_perception_trigger.reset();
}

// 逻辑处理
void ProcProcess::Process() {
    // LogInfo("-----Process  \n");
    // Trigger(&TrafficLightTrigger/::StopLineDistanceLogic, m_traffic_trigger.get(), m_stopline_distance);
    // Trigger(&TrafficLightTrigger::TrafficChangeLogic, m_traffic_trigger.get(), m_Traffic_change);
    // Trigger(&PerceptionDataTrigger::LossDetectObjects, m_perception_trigger.get(), perception_param);
    usleep(100000);
}

// void ProcProcess::Trigger(bool (TrafficLightTrigger::*func)(UnifiedComm *, TrafficCollectParam),
//                           TrafficLightTrigger *obj, TrafficCollectParam param) {
//     (obj->*func)(m_data_reader.get(), param);
// }

// void ProcProcess::Trigger(bool (PerceptionDataTrigger::*func)(UnifiedComm *, PerceptionTriggerParam),
//                           PerceptionDataTrigger *obj, PerceptionTriggerParam param) {
//     (obj->*func)(m_data_reader.get(), param);
// }
