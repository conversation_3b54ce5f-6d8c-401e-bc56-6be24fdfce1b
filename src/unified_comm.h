#ifndef COMMUNICATOR_H
#define COMMUNICATOR_H

#include <unistd.h>
#include <iostream>
#include <thread>

// RCS
#include "buffer_10kb.hh"
#include "buffer_1mb.hh"
#include "buffer_2mb.hh"
// XSCOM
#include <xscom/xscom.h>
// Proto
#include "base/local_pose.pb.h"
#include "channeldefine.h"
#include "common/common.h"
#include "hdmap/local_hdmap.pb.h"
#include "nodedefine.h"
#include "perception/perception_common.pb.h"
#include "perception/perception_object_info.pb.h"
#include "perception/traffic_light_info.pb.h"
#include "trigger_param.h"
#include "xsdaq.hpp"

#define NODE_NAME "data_auto_trigger" //"TriggerCollect"
#define XS_UGV_COM_RCS "rcs"
#define XS_UGV_COM_XSCOM "xscom"

using namespace xsproto;
using namespace xsnetwork;
using namespace xsproto::common;
using LocalPoseMsg = xsproto::base::LocalPose;
using LocalHdmapMsg = xsproto::hdmap::LocalHDMap;
using TrafficLightMsg = xsproto::perception::TrafficLightInfo;
using PerceptionMsg = xsproto::perception::PerceptionObjectInfo;

enum CommType : int { CommType_RCS, CommType_XSCOM };

class UnifiedComm {
public:
    // constructor and destructor
    UnifiedComm();

    ~UnifiedComm();

private:
    // init channel
    void InitRcs();

    void InitXscom();

    // get buffer data
    void GetBufferData();

    // bool ReadLocalPose();
    //  rcs data read function
    bool RcsReadLocalHdmap();

    bool RcsReadTrafficLight();

    bool RcsReadFusedInfo();

    // xscom read function
    bool CrtReadLocalHdmap();

    bool CrtReadFusedInfo();

    bool CrtReadTrafficLight();

    // results send function
    void SendTask(TaskParam param);

    // assist function
    // get program path
    std::string GetProgramPath();

private:
    // get data
    std::deque<PerceptionMsg> GetPerceptionQueue();

    // get hdmap data
    std::deque<LocalHdmapMsg> GetHDmapQueue();

    // get traffic light data
    std::deque<TrafficLightMsg> GetTrafficLightQueue();

    // data queue
    // std::deque<LocalPoseMsg> lp_deque_;
    std::deque<LocalHdmapMsg> hdmap_deque_;
    std::deque<TrafficLightMsg> trafficlight_deque_;
    std::deque<PerceptionMsg> perception_deque_;

    // data lock
    // std::mutex lp_mutex_;
    std::mutex hdmap_mutex_;
    std::mutex trafficlight_mutex_;
    std::mutex perception_mutex_;

private:
    CommType ugv_com_{CommType_RCS};
    std::thread std_thread_;

    // NML *lp_rcs_channel_{nullptr};
    NML *hdmap_rcs_channel_{nullptr};
    NML *tl_rcs_channel_{nullptr};
    NML *perception_rcs_channel_{nullptr};

    // rcs cache
    // std::shared_ptr<LocalPoseMsg> rcs_lp_cache_{nullptr};
    std::shared_ptr<LocalHdmapMsg> rcs_hdmap_cache_{nullptr};
    std::shared_ptr<TrafficLightMsg> rcs_tl_cache_{nullptr};
    std::shared_ptr<PerceptionMsg> rcs_fused_cache_{nullptr};

    // xscom
    std::unique_ptr<xscom::Node> crt_node_{nullptr};
    std::shared_ptr<xscom::Reader<PerceptionMsg> > crt_perception_reader_{nullptr};
    std::shared_ptr<xscom::Reader<LocalHdmapMsg> > crt_hdmap_reader_{nullptr};
    std::shared_ptr<xscom::Reader<TrafficLightMsg> > crt_trafficlight_reader_{nullptr};

private:
    const int kFrameNumPerSec = 10; // 10 frames per second
    const int kPerceptionMaxQueueSize = 2 * kFrameNumPerSec; // 2 seconds
    const int kHdmapMaxQueueSize = 2 * kFrameNumPerSec; // 2 seconds
    const int kTrafficLightMaxQueueSize = 2 * kFrameNumPerSec; // 2 seconds
};

#endif
