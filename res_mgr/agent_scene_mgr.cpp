#include "agent_scene_manager.hpp"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include <numeric>

// AgentState 从 PerceptionObject 构造(obj的数值为全局坐标)
AgentState::AgentState(const PerceptionObject &obj, const LocalPose &ego_pose, double timestamp) :
    timestamp(timestamp), agent_type(AgentType::UNKNOWN), flags(0) {

    // corners 现在是 std::array，无需 reserve

    // 基本几何属性
    center.x = obj.center().x();
    center.y = obj.center().y();
    width = obj.width();
    length = obj.length();
    heading_rad = obj.r_angle();

    // 运动属性
    linear_velocity = obj.linear_speed();
    velocity_heading_rad = obj.linear_speed_angle();

    float local_x = ego_pose.dr_x() - obj.center().x();
    float local_y = ego_pose.dr_y() - obj.center().y();

    // 计算相对自车距离
    distance_to_ego = std::sqrt(local_x * local_x + local_y * local_y);

    // 转换角点坐标系 - 直接访问数组元素，零拷贝
    if (obj.corner().x_size() >= 4 && obj.corner().y_size() >= 4) {
        for (int i = 0; i < 4; ++i) {
            corners[i].x = obj.corner().x(i);
            corners[i].y = obj.corner().y(i);
        }
    } else {
        // 根据中心点、长宽、朝向计算
        float half_length = length * 0.5f;
        float half_width = width * 0.5f;
        float cos_heading = std::cos(heading_rad);
        float sin_heading = std::sin(heading_rad);

        // 左后、右后、右前、左前 - 直接赋值，更高效
        corners[0] = {center.x - half_length * cos_heading - half_width * sin_heading,
                      center.y - half_length * sin_heading + half_width * cos_heading};
        corners[1] = {center.x - half_length * cos_heading + half_width * sin_heading,
                      center.y - half_length * sin_heading - half_width * cos_heading};
        corners[2] = {center.x + half_length * cos_heading + half_width * sin_heading,
                      center.y + half_length * sin_heading - half_width * cos_heading};
        corners[3] = {center.x + half_length * cos_heading - half_width * sin_heading,
                      center.y + half_length * sin_heading + half_width * cos_heading};
    }

    agent_type = GetAgentType(obj.obj_type(), obj.obj_subtype());
}

// 将type从PerceptionObject转成AgentType
AgentType AgentState::GetAgentType(const ObjType &type, const ObjSubType &sub_type) {
    switch (type) {
        case OBJ_TYPE_VEHICLE:
            if (sub_type == OBJ_ST_TRICYCLE) {
                return AgentType::CYCLIST;
            }
            return AgentType::VEHICLE;

        case OBJ_TYPE_PERSON:
            // 根据子类型进一步细分
            switch (sub_type) {
                case OBJ_ST_MOTORLIST: // 摩托车骑行者
                case OBJ_ST_CYCLIST: // 自行车骑行者
                    return AgentType::CYCLIST;
                case OBJ_ST_PEDESTRIAN: // 行人
                case OBJ_ST_ADULT: // 成年人
                case OBJ_ST_CHILD: // 儿童
                case OBJ_ST_POLICEMAN: // 警察
                    return AgentType::PEDESTRIAN;
                default:
                    return AgentType::PEDESTRIAN; // 默认为行人
            }

        case OBJ_TYPE_STATIC: // 静态目标
        case OBJ_TYPE_DRIVABLE_AREA: // 可通行区域
        case OBJ_TYPE_LANE_LINE: // 车道线
        case OBJ_TYPE_UNKNOWN: // 未知类型
        default:
            return AgentType::UNKNOWN;
    }
}

// 添加最新的感知结果(timestamp一定大于当前缓存中的最新时间戳) - 高性能优化版
void SceneManager::AddPerceptionInfo(const PerceptionObjectInfo &perception_info) {

    // 一次性提取所有需要的数据
    const auto &ego_pose = perception_info.header().local_pose();
    const Timestamp timestamp = ego_pose.timestamp();
    const int obj_count = perception_info.obs_objs_size();
    
    // 提前清理过时数据
    CleanExpiredData(timestamp - max_cache_time_seconds);

    // 创建SceneFrame，预分配容器
    auto scene_frame = std::make_shared<SceneFrame>(timestamp);
    scene_frame->agents.reserve(static_cast<size_t>(obj_count));

    // 批量处理所有感知目标 - 减少查找次数
    for (int i = 0; i < obj_count; ++i) {
        const auto &obj = perception_info.obs_objs(i);
        const AgentID agent_id = obj.track_id();

        // 使用 make_shared 一次性分配，减少内存碎片
        auto state = std::make_shared<AgentState>(obj, ego_pose, timestamp);

        // 在两个容器中共享同一个智能指针，避免重复存储
        agent_tracks_[agent_id].emplace(timestamp, state);
        scene_frame->agents.emplace(agent_id, state);
    }

    // 使用 emplace 直接构造，避免中间拷贝
    scene_frames_.emplace(timestamp, std::move(scene_frame));
}

// 添加单个agent状态
void SceneManager::AddAgentState(AgentID agent_id, AgentStatePtr state) {
    if (!state)
        return;

    Timestamp timestamp = state->timestamp;

    // 添加到Agent历史
    agent_tracks_[agent_id].emplace(timestamp, state);

    // 添加到场景帧
    auto scene_it = scene_frames_.find(timestamp);
    if (scene_it == scene_frames_.end()) {
        auto scene = std::make_shared<SceneFrame>(timestamp);
        scene->agents.emplace(agent_id, state);
        scene_frames_.emplace(timestamp, scene);
    } else {
        scene_it->second->agents[agent_id] = state;
    }
}

// 获取缓存统计信息
SceneManager::StatInfo SceneManager::GetStatInfo() const {
    StatInfo stats;

    stats.total_agents = agent_tracks_.size();
    stats.total_scenes = scene_frames_.size();
    stats.total_states = 0;

    // 统计总状态数量
    for (const auto &[agent_id, history]: agent_tracks_) {
        stats.total_states += history.size();
    }

    // 获取时间范围
    if (!scene_frames_.empty()) {
        stats.earliest_timestamp = scene_frames_.begin()->first;
        stats.latest_timestamp = scene_frames_.rbegin()->first;
    } else {
        stats.earliest_timestamp = 0.0;
        stats.latest_timestamp = 0.0;
    }

    return stats;
}

// 清理过期数据(早于cutoff_timestamp)
void SceneManager::CleanExpiredData(Timestamp cutoff_timestamp) {
    // 清理场景帧
    auto scene_it = scene_frames_.lower_bound(cutoff_timestamp);
    scene_frames_.erase(scene_frames_.begin(), scene_it);

    // 清理Agent历史数据
    for (auto agent_it = agent_tracks_.begin(); agent_it != agent_tracks_.end();) {
        auto &history = agent_it->second;

        // 清理该Agent早于指定时间的状态
        auto time_it = history.lower_bound(cutoff_timestamp);
        history.erase(history.begin(), time_it);

        // 如果该Agent没有剩余历史，移除整个Agent
        if (history.empty()) {
            agent_it = agent_tracks_.erase(agent_it);
        } else {
            ++agent_it;
        }
    }
}
