#ifndef AGENT_SCENE_MANAGER_HPP
#define AGENT_SCENE_MANAGER_HPP

#include <algorithm>
#include <array>
#include <chrono>
#include <deque>
#include <functional> // Added for std::function
#include <list>
#include <map> // Added for std::map
#include <memory>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <queue> // Added for std::queue
#include <unordered_map>
#include <vector>

// Proto includes
#include "base/local_pose.pb.h"
#include "perception/perception_common.pb.h"
#include "perception/perception_object_info.pb.h"

using namespace xsproto::perception;
using namespace xsproto::base;

using AgentID = uint32_t;
using Timestamp = double;

// 核心类型
struct AgentState;
using AgentStatePtr = std::shared_ptr<AgentState>;

enum class AgentType {
    UNKNOWN = 0,
    VEHICLE = 1,
    PEDESTRIAN = 2,
    CYCLIST = 3 // 骑行者(自行车、摩托车、三轮车等)
};

// 目标状态信息(位置角度类数值为全局坐标系)
struct AgentState {
    double timestamp{0.0}; // 时间戳
    cv::Point2f center{0.0f, 0.0f}; // 中心点

    // 几何和运动属性
    float width{1.0f}; // 宽度(m)
    float length{1.0f}; // 长度(m)
    float heading_rad{0.0f}; // 朝向角(rad)
    float linear_velocity{0.0f}; // 线速度大小(m/s)
    float velocity_heading_rad{0.0f}; // 速度方向角(rad)
    float distance_to_ego{0.0f}; // 相对自车距离(m)

    // 枚举和布尔值紧凑排列
    AgentType agent_type{AgentType::UNKNOWN}; // 目标类型
    uint32_t flags{0}; // 位域标志：bit0=is_occluded, bit1=is_in_roi, bit2-31保留

    std::array<cv::Point2f, 4> corners; // 四个角点(全局坐标系) - 固定大小数组，零拷贝

    // 默认构造函数 - 优化内存布局
    AgentState() noexcept :
        timestamp(0.0), center(0.0f, 0.0f), width(0.0f), length(0.0f), heading_rad(0.0f), linear_velocity(0.0f),
        velocity_heading_rad(0.0f), distance_to_ego(0.0f), agent_type(AgentType::UNKNOWN), flags(0), corners{} {
    }

    // 从PerceptionObject构造
    AgentState(const PerceptionObject &obj, const LocalPose &ego_pose, double timestamp);

    // 根据感知对象类型获取AgentType
    static AgentType GetAgentType(const ObjType &type, const ObjSubType &sub_type);

    // 判断是否为运动目标
    inline bool IsDynamic(float speed_threshold = 0.5f) const noexcept { return linear_velocity > speed_threshold; }

    // 判断是否被遮挡
    inline bool IsOccluded() const noexcept { return flags & 0x1; }

    // 设置是否被遮挡
    inline void SetOccluded(bool occluded) noexcept {
        if (occluded)
            flags |= 0x1;
        else
            flags &= ~0x1;
    }

    // 判断是否在感兴趣区域内
    inline bool IsInROI() const noexcept { return flags & 0x2; }

    // 设置是否在感兴趣区域内
    inline void SetInROI(bool in_roi) noexcept {
        if (in_roi)
            flags |= 0x2;
        else
            flags &= ~0x2;
    }
};

/**
 * @brief 场景帧 - 单帧的所有Agent状态
 */
class SceneFrame {
public:
    Timestamp timestamp;
    // agent_id -> AgentStatePtr，可直接访问
    std::unordered_map<AgentID, AgentStatePtr> agents;

    explicit SceneFrame(Timestamp ts) : timestamp(ts) {}

    // 插入或更新agent状态
    void InsertState(AgentID agent_id, AgentStatePtr state) { agents[agent_id] = std::move(state); }

    // 移除agent
    void RemoveAgent(AgentID agent_id) { agents.erase(agent_id); }

    // 获取agent状态
    AgentStatePtr GetAgent(AgentID agent_id) const {
        auto it = agents.find(agent_id);
        return it != agents.end() ? it->second : nullptr;
    }

    // 获取所有agent ID - 避免拷贝优化  
    void GetAgentIDs(std::vector<AgentID>& ids) const noexcept {
        ids.clear();
        ids.reserve(agents.size());
        for (const auto &[id, _]: agents) {
            ids.push_back(id);
        }
    }

    // 获取agent数量
    size_t GetAgentCount() const { return agents.size(); }

    // 判断是否包含指定agent
    bool HasAgent(AgentID agent_id) const { return agents.find(agent_id) != agents.end(); }

    // 清空
    void Clear() { agents.clear(); }
};

/**
 * @brief 感知结果缓存管理类
 * 整合了Agent历史管理和场景序列管理，参考设计思路但保持接口友好
 */
class SceneManager {
public:
    SceneManager() = default;
    ~SceneManager() = default;

    // ===== 核心数据添加接口 =====

    // 添加最新的感知结果(timestamp一定大于当前缓存中的最新时间戳)
    void AddPerceptionInfo(const PerceptionObjectInfo &perception_info);

    // 添加单个agent状态
    void AddAgentState(AgentID agent_id, AgentStatePtr state);

    // ===== Agent历史轨迹查询接口 =====

    // 获取agent的所有历史状态 (按时间排序) - 内联优化
    inline const std::map<Timestamp, AgentStatePtr> *GetAgentHistory(AgentID agent_id) const noexcept {
        auto it = agent_tracks_.find(agent_id);
        return (it != agent_tracks_.end()) ? &it->second : nullptr;
    }

    // 获取agent在指定时间的状态 - 内联优化
    inline AgentStatePtr GetAgentState(AgentID agent_id, Timestamp timestamp) const noexcept {
        auto it = agent_tracks_.find(agent_id);
        if (it != agent_tracks_.end()) [[likely]] {
            auto jt = it->second.find(timestamp);
            return jt != it->second.end() ? jt->second : nullptr;
        }
        return nullptr;
    }

    // 获取agent的最新状态 - 内联优化
    inline AgentStatePtr GetAgentLatestState(AgentID agent_id) const noexcept {
        auto it = agent_tracks_.find(agent_id);
        if (it != agent_tracks_.end() && !it->second.empty()) [[likely]] {
            return it->second.rbegin()->second; // map最后一个元素(最大timestamp)
        }
        return nullptr;
    }

    // 获取agent在指定时间范围内的所有状态 - 避免拷贝优化
    void GetAgentStatesInRange(AgentID agent_id, Timestamp start_time, Timestamp end_time, 
                               std::vector<AgentStatePtr>& results) const noexcept {
        results.clear();
        auto it = agent_tracks_.find(agent_id);
        if (it != agent_tracks_.end()) [[likely]] {
            auto start_it = it->second.lower_bound(start_time);
            auto end_it = it->second.upper_bound(end_time);
            results.reserve(std::distance(start_it, end_it));
            for (auto iter = start_it; iter != end_it; ++iter) {
                results.push_back(iter->second);
            }
        }
    }

    // 移除agent的指定时间状态
    bool RemoveAgentState(AgentID agent_id, Timestamp timestamp) {
        auto it = agent_tracks_.find(agent_id);
        if (it != agent_tracks_.end()) {
            size_t removed = it->second.erase(timestamp);
            if (it->second.empty()) {
                agent_tracks_.erase(it);
            }
            return removed > 0;
        }
        return false;
    }

    // 移除agent的所有历史数据
    bool RemoveAgent(AgentID agent_id) { return agent_tracks_.erase(agent_id) > 0; }

    // 获取所有已知的agent ID - 避免拷贝优化
    void GetAllAgentIDs(std::vector<AgentID>& ids) const noexcept {
        ids.clear();
        ids.reserve(agent_tracks_.size());
        for (const auto &[id, _]: agent_tracks_) {
            ids.push_back(id);
        }
    }
    
    // 获取agent数量 - 内联优化
    inline size_t GetAgentCount() const noexcept { return agent_tracks_.size(); }

    // ===== 场景时序查询接口 =====

    // 获取指定时刻的场景帧 - 内联优化
    inline std::shared_ptr<SceneFrame> GetSceneFrame(Timestamp timestamp) const noexcept {
        auto it = scene_frames_.find(timestamp);
        return it != scene_frames_.end() ? it->second : nullptr;
    }

    // 获取指定时刻的所有agent状态 - 避免拷贝优化
    void GetSceneAgents(Timestamp timestamp, std::unordered_map<AgentID, AgentStatePtr>& result) const noexcept {
        auto scene = GetSceneFrame(timestamp);
        if (scene) [[likely]] {
            result = scene->agents;
        } else {
            result.clear();
        }
    }

    // 获取指定时间范围内的所有时刻 - 避免拷贝优化
    void GetTimestampsInRange(Timestamp start_time, Timestamp end_time, 
                              std::vector<Timestamp>& timestamps) const noexcept {
        timestamps.clear();
        auto start_it = scene_frames_.lower_bound(start_time);
        auto end_it = scene_frames_.upper_bound(end_time);
        timestamps.reserve(std::distance(start_it, end_it));
        for (auto iter = start_it; iter != end_it; ++iter) {
            timestamps.push_back(iter->first);
        }
    }

    // 获取最新的场景帧 - 内联优化
    inline std::shared_ptr<SceneFrame> GetLatestSceneFrame() const noexcept {
        return scene_frames_.empty() ? nullptr : scene_frames_.rbegin()->second;
    }

    // 检索某个id在所有时间帧中的状态(跨场景查询) - 避免拷贝优化
    void GetAgentStatesOverTime(AgentID agent_id, std::vector<AgentStatePtr>& results) const noexcept {
        results.clear();
        results.reserve(scene_frames_.size()); // 预分配最大可能容量
        for (const auto &[ts, scene]: scene_frames_) {
            auto state = scene->GetAgent(agent_id);
            if (state) [[likely]]
                results.push_back(state);
        }
    }

    // ===== 维护和管理接口 =====

    // 设置最大保留时间窗口
    void SetMaxRetentionTime(double max_time) { max_cache_time_seconds = max_time; }

    // 获取缓存统计信息
    struct StatInfo {
        size_t total_agents;
        size_t total_scenes;
        size_t total_states;
        Timestamp earliest_timestamp;
        Timestamp latest_timestamp;
    };
    StatInfo GetStatInfo() const;

    // 清空所有数据
    void Clear() {
        agent_tracks_.clear();
        scene_frames_.clear();
    }

private:
    // ===== 核心数据结构 =====

    // Agent历史管理：agent_id -> (timestamp -> AgentStatePtr)，按时间排序
    std::unordered_map<AgentID, std::map<Timestamp, AgentStatePtr>> agent_tracks_;

    // 场景序列管理：timestamp -> SceneFrame，按时间排序
    std::map<Timestamp, std::shared_ptr<SceneFrame>> scene_frames_;

    // 配置参数
    double max_cache_time_seconds = 30.0; // 默认30s

    // 线程安全锁(如果需要多线程访问)
    // mutable std::mutex cache_mutex_;

    // 内部辅助函数
    void CleanExpiredData(Timestamp cutoff_timestamp = 0); // 清理过期数据
};

#endif // PERCEPTION_AGENT_CACHE_HPP
