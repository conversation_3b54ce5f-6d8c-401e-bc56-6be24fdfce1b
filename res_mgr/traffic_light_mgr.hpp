#ifndef TRAFFIC_LIGHT_MGR_HPP
#define TRAFFIC_LIGHT_MGR_HPP

#include <chrono>
#include <map>
#include <memory>
#include <mutex>
#include <utility>
#include <vector>
#include "perception/traffic_light_info.pb.h"

// 红绿灯类型枚举 - 便于按类型查询
enum class LightType : uint8_t {
    FORWARD = 0, // 直行
    LEFT = 1, // 左转
    RIGHT = 2, // 右转
    UTURN = 3 // 调头
};

// 红绿灯状态结构 - 极致内存优化（40字节 -> 16字节）
struct TrafficLightFrame {
    double timestamp; // 时间戳（秒）- 8字节

    // 使用位域压缩存储 - 8字节总计
    union {
        uint64_t packed_data; // 打包存储所有状态
        struct {
            uint64_t forward_type : 4; // 0-15, 足够存储 0-3 状态
            uint64_t left_type : 4; // 0-15
            uint64_t right_type : 4; // 0-15
            uint64_t uturn_type : 4; // 0-15
            uint64_t forward_flash : 4; // 0-15, 足够存储 0-2 闪烁状态
            uint64_t left_flash : 4; // 0-15
            uint64_t right_flash : 4; // 0-15
            uint64_t uturn_flash : 4; // 0-15
            uint64_t reserved : 32; // 保留位，未来扩展
        };
    };

    // 默认构造 - 零初始化
    TrafficLightFrame() noexcept : timestamp(0.0), packed_data(0) {}

    // 参数构造 - 高效打包
    TrafficLightFrame(double ts, uint32_t fw, uint32_t lt, uint32_t rt, uint32_t ut, int32_t fw_flash, int32_t lt_flash,
                      int32_t rt_flash, int32_t ut_flash) noexcept : timestamp(ts), packed_data(0) {
        forward_type = fw;
        left_type = lt;
        right_type = rt;
        uturn_type = ut;
        forward_flash = fw_flash;
        left_flash = lt_flash;
        right_flash = rt_flash;
        uturn_flash = ut_flash;
    }

    // 检查是否有检测结果 - SIMD友好的位运算
    inline bool HasTrafficLightState() const noexcept {
        // 检查前16位（4个灯的状态）是否有非零值
        return (packed_data & 0xFFFF) != 0;
    }

    // 获取指定灯类型状态 - 分支预测优化
    inline uint32_t GetLightType(LightType light_type) const noexcept {
        const uint64_t shift = static_cast<uint64_t>(light_type) << 2; // * 4
        return (packed_data >> shift) & 0xF;
    }

    // 获取指定灯闪烁状态 - 高效位操作
    inline int32_t GetLightFlash(LightType light_type) const noexcept {
        const uint64_t shift = 16 + (static_cast<uint64_t>(light_type) << 2); // 16 + * 4
        return static_cast<int32_t>((packed_data >> shift) & 0xF);
    }

    // 批量设置状态 - 一次性写入
    inline void SetAllStates(uint32_t fw, uint32_t lt, uint32_t rt, uint32_t ut, int32_t fw_flash, int32_t lt_flash,
                             int32_t rt_flash, int32_t ut_flash) noexcept {
        packed_data = static_cast<uint64_t>(fw) | (static_cast<uint64_t>(lt) << 4) | (static_cast<uint64_t>(rt) << 8) |
                      (static_cast<uint64_t>(ut) << 12) | (static_cast<uint64_t>(fw_flash) << 16) |
                      (static_cast<uint64_t>(lt_flash) << 20) | (static_cast<uint64_t>(rt_flash) << 24) |
                      (static_cast<uint64_t>(ut_flash) << 28);
    }
};

// 灯状态信息结构 - 时间维度查询结果
struct LightStateInfo {
    double timestamp;
    uint32_t light_type;
    int32_t flash_state;

    LightStateInfo(double ts, uint32_t type, int32_t flash) noexcept :
        timestamp(ts), light_type(type), flash_state(flash) {}
};

// 红绿灯管理类 - 高性能设计
class TrafficLightManager {
public:
    TrafficLightManager() noexcept;
    ~TrafficLightManager() = default;

    // ===== 核心数据操作接口 =====

    // 添加新的红绿灯状态帧
    void AddFrame(const TrafficLightFrame &tl_frame) noexcept;


    // ===== 空间维度查询接口 =====

    // 检查指定时间戳的帧是否有检测结果
    bool HasDetectionAt(double timestamp) const noexcept;

    // 获取指定时间戳的完整状态
    bool GetFrameByTimestamp(double timestamp, TrafficLightFrame &state) const noexcept;

    // 获取最新的红绿灯状态
    bool GetLatestFrame(TrafficLightFrame &state) const noexcept;

    // ===== 时间维度查询接口 =====

    // 获取指定灯类型在时间范围内的所有状态 - 零拷贝优化
    void GetLightStatesInRange(LightType light_type, double start_time, double end_time,
                               std::vector<LightStateInfo> &results) const noexcept;

    // // 获取指定时间范围内的所有完整状态帧 - 零拷贝优化
    // void GetFramesInRange(double start_time, double end_time,
    //                       std::vector<std::pair<double, const TrafficLightFrame*>>& results) const noexcept;

    // 获取指定灯类型的最新状态
    bool GetLatestLightState(LightType light_type, uint32_t &light_state, int32_t &flash_state) const noexcept;

    // ===== 维护管理接口 =====

    // 清理过期数据 - 高效范围删除
    void CleanExpiredData(double cutoff_timestamp) noexcept;

    // 设置最大缓存时间
    void SetCacheDuration(double max_duration) noexcept { max_cache_duration_seconds_ = max_duration; }

    // 清空所有缓存数据
    void Clear() noexcept;

    // 获取统计信息
    inline double GetEarliestTimestamp() const noexcept {
        return timeline_states_.empty() ? 0.0 : timeline_states_.begin()->first;
    }
    inline double GetLatestTimestamp() const noexcept {
        return timeline_states_.empty() ? 0.0 : timeline_states_.rbegin()->first;
    }

private:
    // ===== 核心数据结构 =====

    // 主数据容器：时间戳 -> 红绿灯状态帧，按时间自动排序，支持高效范围查询
    std::map<double, TrafficLightFrame> timeline_states_;

    // 高频访问缓存 - 避免重复查找最新状态
    mutable TrafficLightFrame latest_frame_cache_;
    mutable double latest_cache_timestamp_ = -1.0;
    mutable bool cache_valid_ = false;

    // 配置参数
    double max_cache_duration_seconds_ = 30.0; // 默认缓存30秒. 1秒有10个数据(检测频率10hz)
    size_t cleanup_threshold_ = 300; // 超过300条记录时触发清理

    // 线程安全锁（如果需要多线程访问时启用）
    // mutable std::mutex cache_mutex_;

    // ===== 内部辅助函数 =====

    // 从protobuf消息解析状态帧
    TrafficLightFrame ParseTrafficLightMsg(const xsproto::perception::TrafficLightInfo &tl_info) const;
};

#endif // TRAFFIC_LIGHT_MGR_HPP
