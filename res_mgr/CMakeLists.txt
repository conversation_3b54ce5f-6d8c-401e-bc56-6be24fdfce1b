# Traffic Light Cache Library
# 红绿灯缓存库

file(GLOB RES_CACHE_SRCS 
    "tl_res_cache.cpp"
    "tl_cache_usage_example.cpp"
    # 为将来可能添加的其他缓存文件预留
    # "hdmap_res_cache.cpp"
    # "perception_res_cache.cpp"
)

file(GLOB RES_CACHE_HDRS 
    "tl_res_cache.hpp"
    "tl_cache_usage_example.hpp"
    # 为将来可能添加的其他缓存文件预留
    # "hdmap_res_cache.hpp" 
    # "perception_res_cache.hpp"
)

# 创建缓存库
add_library(res_cache STATIC ${RES_CACHE_SRCS} ${RES_CACHE_HDRS})

# 设置包含目录
target_include_directories(res_cache PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/src
    ${PROJECT_SOURCE_DIR}/include
    ${PROJECT_SOURCE_DIR}/../xsproto_include
    ${Boost_INCLUDE_DIRS}
)

# 查找boost库
find_package(Boost REQUIRED COMPONENTS system filesystem)

# 链接依赖库
target_link_libraries(res_cache
    pthread
    protobuf
    ${Boost_LIBRARIES}
)

# 设置编译标志
target_compile_features(res_cache PRIVATE cxx_std_14) 