#include "traffic_light_mgr.hpp"
#include <algorithm>
#include <chrono>
#include <iostream>
#include <limits>

// TrafficLightManager 实现
TrafficLightManager::TrafficLightManager() noexcept :
    max_cache_duration_seconds_(30.0), cleanup_threshold_(300), latest_cache_timestamp_(-1.0), cache_valid_(false) {}

// 添加新的红绿灯状态帧 - 极致性能优化
void TrafficLightManager::AddFrame(const TrafficLightFrame &tl_frame) noexcept {
    // 使用emplace直接构造，避免拷贝
    timeline_states_.emplace(tl_frame.timestamp, tl_frame);

    // 更新最新状态缓存
    if (tl_frame.timestamp > latest_cache_timestamp_) {
        latest_frame_cache_ = tl_frame;
        latest_cache_timestamp_ = tl_frame.timestamp;
        cache_valid_ = true;
    }

    // 智能清理策略：当数据量超过阈值时清理
    if (timeline_states_.size() > cleanup_threshold_) [[unlikely]] {
        double cutoff_time = tl_frame.timestamp - max_cache_duration_seconds_;
        CleanExpiredData(cutoff_time);
    }
}

// 检查指定时间戳的帧是否有检测结果 - 内联优化
bool TrafficLightManager::HasDetectionAt(double timestamp) const noexcept {
    auto it = timeline_states_.find(timestamp);
    return it != timeline_states_.end() && it->second.HasTrafficLightState();
}

// 获取指定时间戳的完整状态 - 高效缓存+最近邻查找
bool TrafficLightManager::GetFrameByTimestamp(double timestamp, TrafficLightFrame &state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 快速路径：检查是否请求最新数据
    if (cache_valid_ && timestamp >= latest_cache_timestamp_) [[likely]] {
        state = latest_frame_cache_;
        return true;
    }

    // 精确查找
    auto exact_it = timeline_states_.find(timestamp);
    if (exact_it != timeline_states_.end()) [[likely]] {
        state = exact_it->second;
        return true;
    }

    // 最近邻查找 - 简化逻辑，提升性能
    auto lower_it = timeline_states_.lower_bound(timestamp);

    if (lower_it == timeline_states_.end()) {
        // 返回最新数据
        state = timeline_states_.rbegin()->second;
    } else if (lower_it == timeline_states_.begin()) {
        // 返回最早数据
        state = lower_it->second;
    } else {
        // 比较相邻两个时间戳，选择更近的
        auto prev_it = std::prev(lower_it);
        state = (timestamp - prev_it->first <= lower_it->first - timestamp) ? prev_it->second : lower_it->second;
    }

    return true;
}

// 获取最新的红绿灯状态 - 极致O(1)缓存访问
bool TrafficLightManager::GetLatestFrame(TrafficLightFrame &state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 优先使用缓存，避免map查找
    if (cache_valid_) [[likely]] {
        state = latest_frame_cache_;
        return true;
    }

    // 缓存失效时重建缓存
    const auto &latest_pair = *timeline_states_.rbegin();
    latest_frame_cache_ = latest_pair.second;
    latest_cache_timestamp_ = latest_pair.first;
    cache_valid_ = true;

    state = latest_frame_cache_;
    return true;
}

// 获取指定灯类型在时间范围内的所有状态 - 零拷贝优化
void TrafficLightManager::GetLightStatesInRange(LightType light_type, double start_time, double end_time,
                                                std::vector<LightStateInfo> &results) const noexcept {
    results.clear();

    if (timeline_states_.empty() || start_time > end_time) {
        return;
    }

    // 使用map的范围查询，高效O(log n + k)
    auto start_it = timeline_states_.lower_bound(start_time);
    auto end_it = timeline_states_.upper_bound(end_time);

    // 预分配容器大小，提升性能
    size_t estimated_size = std::distance(start_it, end_it);
    results.reserve(estimated_size);

    // 遍历范围内的数据，提取指定灯类型的状态
    for (auto it = start_it; it != end_it; ++it) {
        const auto &frame = it->second;
        uint32_t light_state = frame.GetLightType(light_type);
        int32_t flash_state = frame.GetLightFlash(light_type);

        // 使用emplace_back避免临时对象拷贝
        results.emplace_back(frame.timestamp, light_state, flash_state);
    }
}

// 获取指定灯类型的最新状态 - 缓存优化
bool TrafficLightManager::GetLatestLightState(LightType light_type, uint32_t &light_state,
                                              int32_t &flash_state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 使用缓存避免map查找
    const TrafficLightFrame *frame = nullptr;
    if (cache_valid_) [[likely]] {
        frame = &latest_frame_cache_;
    } else {
        // 重建缓存
        const auto &latest_pair = *timeline_states_.rbegin();
        latest_frame_cache_ = latest_pair.second;
        latest_cache_timestamp_ = latest_pair.first;
        cache_valid_ = true;
        frame = &latest_frame_cache_;
    }

    light_state = frame->GetLightType(light_type);
    flash_state = frame->GetLightFlash(light_type);

    return true;
}

// 清理过期数据 - 高效范围删除+缓存维护
void TrafficLightManager::CleanExpiredData(double cutoff_timestamp) noexcept {
    if (timeline_states_.empty()) {
        return;
    }

    // 检查缓存是否会被删除
    if (cache_valid_ && latest_cache_timestamp_ < cutoff_timestamp) {
        cache_valid_ = false;
    }

    // 高效范围删除：O(log n + k)
    auto cutoff_it = timeline_states_.lower_bound(cutoff_timestamp);
    timeline_states_.erase(timeline_states_.begin(), cutoff_it);
}

// 清空所有缓存数据
void TrafficLightManager::Clear() noexcept {
    timeline_states_.clear();
    cache_valid_ = false;
    latest_cache_timestamp_ = -1.0;
}

// 从protobuf消息解析状态帧 - 适配位域结构优化
TrafficLightFrame
TrafficLightManager::ParseTrafficLightMsg(const xsproto::perception::TrafficLightInfo &tl_info) const {
    TrafficLightFrame frame;

    // 提取时间戳
    if (tl_info.header().local_pose().timestamp() > 0.0) {
        frame.timestamp = tl_info.header().local_pose().timestamp();
    } else {
        // 使用高精度时间戳
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = now.time_since_epoch();
        frame.timestamp = std::chrono::duration<double>(duration).count();
        std::cerr << "[TrafficLightManager] Warning: No valid timestamp, using current time" << std::endl;
    }

    // 提取红绿灯状态并批量设置（利用新的位域优化）
    if (tl_info.has_traffic_light()) {
        const auto &tl = tl_info.traffic_light();
        frame.SetAllStates(tl.forward_type(), tl.left_type(), tl.right_type(), tl.uturn_type(), tl.forward_flash(),
                           tl.left_flash(), tl.right_flash(), tl.uturn_flash());
    }

    return frame;
}
