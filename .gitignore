# ====================
# C++ 项目 .gitignore
# ====================

# ================== 编译输出 ==================
# 编译产生的目标文件
*.o
*.obj
*.a
*.lib
*.so
*.so.*
*.dll
*.dylib

# 可执行文件
*.exe
*.out
*.app

# 静态和动态库
lib*.a
lib*.so
lib*.so.*
*.dll.a

# 编译器生成的临时文件
*.i
*.ii
*.s
*.asm
*.bc

# ================== CMake 构建 ==================
# CMake 生成的文件和目录
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# CMake 调试构建目录
cmake-build-*/
cmake-build-debug/
cmake-build-release/

# ================== 构建目录 ==================
# 通用构建目录
build/
Build/
builds/
out/
dist/

# 项目特定构建输出目录
0-bin/*
!0-bin/parameters/*.json
0-lib/*

# ================== IDE 配置文件 ==================
# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# CLion
.idea/
cmake-build-*/

# Visual Studio
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Xcode
*.xcodeproj/
*.xcworkspace/

# Qt Creator
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
CMakeLists.txt.user*

# ================== 编译器和工具 ==================
# GCC/Clang
*.gch
*.pch

# MSVC
*.pdb
*.idb
*.ilk
*.meta
*.tlog/
*.log
*.manifest
*.res
*.rc.tmp
*.cache

# Intel C++ Compiler
*.dyn

# ================== 调试文件 ==================
# 调试信息
*.dSYM/
*.su
*.tmp

# Valgrind
vgcore.*
*.memcheck

# GDB
.gdb_history
core
core.*

# ================== 性能分析 ==================
# Profiling
*.profile
gmon.out
perf.data*
*.gcov
*.gcda
*.gcno

# ================== 包管理器 ==================
# Conan
conandata.txt
conanfile.py
conaninfo.txt
conanbuildinfo.*
conan.lock

# vcpkg
vcpkg_installed/

# CPM
_deps/

# ================== 协议缓冲区 ==================
# Protocol Buffers 生成的文件
*.pb.h
*.pb.cc
*.pb.cpp
google_proto/
google_proto

# ================== 文档生成 ==================
# Doxygen
docs/html/
docs/latex/
docs/xml/
Doxyfile.bak

# ================== 临时文件 ==================
# 编辑器备份文件
*~
*.swp
*.swo
*#
.#*

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================== 日志文件 ==================
*.log
*.log.*
logs/

# ================== 配置文件 ==================
# 本地配置（保留模板）
config/local.*
!config/local.example.*

# ================== 测试相关 ==================
# 测试输出
test_results/
test_output/
*.tap
*.xml

# Google Test
gtest/
googletest/

# ================== 其他 ==================
# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.tar.xz
*.7z

# 生成的文档
*.pdf
*.ps

# 依赖管理
third_party/
!third_party/CMakeLists.txt
!third_party/README.md

# 项目特定忽略
parameters/local_*
!parameters/example_* 