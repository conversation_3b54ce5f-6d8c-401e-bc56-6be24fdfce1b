#ifndef XSDAP_HPP
#define XSDAP_HPP

#include <iostream>
#include <array>
#include <functional>
#include <memory>
#include <algorithm>
#include <string>
#include <list>
#include <chrono>
#include "udpsocket.hpp"
#include "xsprotocol.hpp"


//#define register_rsp_cb(rqst_msg_id, rqst_msg_body_ref, rsp_msg_id, rsp_msg_body_ptr, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
//do {\
//if (0 >= sendMessage(rqst_msg_id, rqst_msg_body_ref, peer_addr, peer_port)) {\
//return NETWORK_REJECT;\
//} \
//if (0>=wait_time) { \
//    if (cb) { \
//        registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(\
//            rsp_msg_id, [cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){ \
//                cb(msg); \
//            }); \
//    } \
//    return SUSPEND; \
//}\
//bool get_flag = false;\
//auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){\
//    get_flag = true; \
//    if (nullptr != rsp_msg_body_ptr) *rsp_msg_body_ptr = msg; \
//    on_get_rsp \
//    }; \
//    registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(rsp_msg_id,f); \
//    while (!get_flag && 0 < wait_time--) { \
//        processMessages(); \
//        usleep(1000); \
//    } \
//}while(false)


//---request no body, response has body
//#define register_rqstnobody_cb(rqst_msg_id, rsp_msg_id, rsp_msg_body_ptr, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
//do {\
//if (0 >= sendMessage(rqst_msg_id, peer_addr, peer_port)) {\
//    return NETWORK_REJECT;\
//} \
//if (0>=wait_time) { \
//    if (cb) { \
//        registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(\
//            rsp_msg_id, [cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){ \
//            cb(msg); \
//        }); \
//    } \
//    return SUSPEND; \
//}\
//bool get_flag = false;\
//auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){\
//    get_flag = true; \
//    if (nullptr != rsp_msg_body_ptr) *rsp_msg_body_ptr = msg; \
//    on_get_rsp \
//    }; \
//    registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(rsp_msg_id,f); \
//    while (!get_flag && 0 < wait_time--) { \
//        processMessages(); \
//        usleep(1000); \
//    } \
//}while(false)


//---request has body, response no body
//#define register_respnobody_cb(rqst_msg_id, rqst_msg_body_ref, rsp_msg_id, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
//do {\
//if (0 >= sendMessage(rqst_msg_id, rqst_msg_body_ref, peer_addr, peer_port)) {\
//return NETWORK_REJECT;\
//} \
//if (0>=wait_time) { \
//    if (cb) { \
//        registerMessage(\
//            rsp_msg_id, [cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){ \
//                cb(msg); \
//            }); \
//    } \
//    return SUSPEND; \
//}\
//bool get_flag = false;\
//auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){\
//    get_flag = true; \
//    on_get_rsp \
//    }; \
//    registerMessage(rsp_msg_id,f); \
//    while (!get_flag && 0 < wait_time--) { \
//        processMessages(); \
//        usleep(1000); \
//    } \
//}while(false)


//---request no body, response no body
//#define register_rqstnobody_respnobody_cb(rqst_msg_id, rsp_msg_id, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
//do {\
//if (0 >= sendMessage(rqst_msg_id, peer_addr, peer_port)) {\
//return NETWORK_REJECT;\
//} \
//if (0>=wait_time) { \
//    if (cb) { \
//        registerMessage(\
//            rsp_msg_id, [cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){ \
//                cb(msg); \
//            }); \
//    } \
//    return SUSPEND; \
//}\
//bool get_flag = false;\
//auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){\
//    get_flag = true; \
//    on_get_rsp \
//    }; \
//    registerMessage(rsp_msg_id,f); \
//    while (!get_flag && 0 < wait_time--) { \
//        processMessages(); \
//        usleep(1000); \
//    } \
//}while(false)

//    ErrorCode start(const xsdaq::msg::Task& task, xsdaq::msg::TaskError *err_msg = nullptr, int64_t millseconds = 0, StartCallback cb = StartCallback()) noexcept {
//        ErrorCode err = TIMEOUT;
//        unsigned int msg_seq = current_message_sequence();
//        if (0 < sendMessage(MID_START_TASK_RQST, task, _server_addr, _server_port, msg_seq)) {
//            if (0>=millseconds) {
//                if (cb) {
//                    registerMessage<xsdaq::msg::TaskError>(MID_START_TASK_RESP, [this,cb,msg_seq](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, const xsdaq::msg::TaskError &msg){
//                        if (nullptr != _cur_msg_header && _cur_msg_header->_msg_seq == msg_seq) {
//                            cb(msg);
//                        }
//                    });
//                }
//                return SUSPEND;
//            }

//            auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, const xsdaq::msg::TaskError &msg){
//                if (nullptr != err_msg ) {
//                    *err_msg = msg;
//                }
//                err = msg._err_code == 0? SUCC: PEER_REJECT;
//            };
//            registerMessage<xsdaq::msg::TaskError>(xsdaq::MID_START_TASK_RESP,f);
//            while ((SUCC != err) && 0 < millseconds--) {
//                if (0 >= processMessages())
//                    usleep(1000);
//            }
//            if (TIMEOUT == err) {
//                unresigserMessage(MID_START_TASK_RESP);
//            }
//        }
//        else {
//            err = UNKOWN;
//        }
//        return err;
//    }


#define RCS_LOCALPOSE                                 "LocalPose"
#define RCS_GLOBAL_POSITION                           "GlobalPositionBuffer"
#define RCS_FGLOBAL_POSITION                          "FGlobalPositionBuffer"
#define RCS_LOCAL_HDMAP                               "LocalHDMap"
#define RCS_TRAFFIC_MAP                               "TrafficMap"
#define RCS_TrafficMapIntersection                    "TrafficMapIntersection"
#define RCS_TASK_LIST                                 "TaskList"
#define RCS_ULTRA_SONIC_DATA                          "UltraSonicData"
#define RCS_LOCAL_ATTRIBUTE_MAP	                   "LocalAttributeMap"
#define RCS_ENTITY_MAP	                               "EntityMap"
#define RCS_LADAR_MIDDLE_MIDDLE_TOP	               "LadarMiddleMiddleTop"
#define RCS_LADAR_MIDDLE_FRONT_BOTTOM	               "LadarMiddleFrontBottom"
#define RCS_LADAR_RIGHT_FRONT_BOTTOM	               "LadarRightFrontBottom"
#define RCS_LADAR_RIGHT_MIDDLE_BOTTOM	               "LadarRightMiddleBottom"
#define RCS_LADAR_RIGHT_REAR_BOTTOM	               "LadarRightRearBottom"
#define RCS_LADAR_MIDDLE_REAR_BOTTOM	               "LadarMiddleRearBottom"
#define RCS_LADAR_LEFT_REAR_BOTTOM	                   "LadarLeftRearBottom"
#define RCS_LADAR_LEFT_MIDDLE_BOTTOM	               "LadarLeftMiddleBottom"
#define RCS_LADAR_LEFT_FRONT_BOTTOM	               "LadarLeftFrontBottom"
#define RCS_LADAR_MIDDLE_FRONT_TOP	                   "LadarMiddleFrontTop"
#define RCS_LADAR_RIGHT_FRONT_TOP	                   "LadarRightFrontTop"
#define RCS_LADAR_RIGHT_MIDDLE_TOP	                   "LadarRightMiddleTop"
#define RCS_LADAR_RIGHT_REAR_TOP	                   "LadarRightRearTop"
#define RCS_LADAR_MIDDLE_REAR_TOP	                   "LadarMiddleRearTop"
#define RCS_LADAR_LEFT_REAR_TOP	                   "LadarLeftRearTop"
#define RCS_LADAR_LEFT_MIDDLE_TOP	                   "LadarLeftMiddleTop"
#define RCS_LADAR_LEFT_FRONT_TOP	                   "LadarLeftFrontTop"
#define RCS_LADAR_FLOAT_MIDDLE_MIDDLE_TOP	           "LadarFloatMiddleMiddleTop"
#define RCS_LADAR_FLOAT_MIDDLE_FRONT_BOTTOM	       "LadarFloatMiddleFrontBottom"
#define RCS_LADAR_FLOAT_RIGHT_FRONT_BOTTOM	           "LadarFloatRightFrontBottom"
#define RCS_LADAR_FLOAT_RIGHT_MIDDLE_BOTTOM	       "LadarFloatRightMiddleBottom"
#define RCS_LADAR_FLOAT_RIGHT_REAR_BOTTOM	           "LadarFloatRightRearBottom"
#define RCS_LADAR_FLOAT_MIDDLE_REAR_BOTTOM	           "LadarFloatMiddleRearBottom"
#define RCS_LADAR_FLOAT_LEFT_REAR_BOTTOM	           "LadarFloatLeftRearBottom"
#define RCS_LADAR_FLOAT_LEFT_MIDDLE_BOTTOM	           "LadarFloatLeftMiddleBottom"
#define RCS_LADAR_FLOAT_LEFT_FRONT_BOTTOM	           "LadarFloatLeftFrontBottom"
#define RCS_LADAR_FLOAT_MIDDLE_FRONT_TOP	           "LadarFloatMiddleFrontTop"
#define RCS_LADAR_FLOAT_RIGHT_FRONT_TOP	           "LadarFloatRightFrontTop"
#define RCS_LADAR_FLOAT_RIGHT_MIDDLE_TOP	           "LadarFloatRightMiddleTop"
#define RCS_LADAR_FLOAT_RIGHT_REAR_TOP	               "LadarFloatRightRearTop"
#define RCS_LADAR_FLOAT_MIDDLE_REAR_TOP	           "LadarFloatMiddleRearTop"
#define RCS_LADAR_FLOAT_LEFT_REAR_TOP	               "LadarFloatLeftRearTop"
#define RCS_LADAR_FLOAT_LEFT_MIDDLE_TOP	           "LadarFloatLeftMiddleTop"
#define RCS_LADAR_FLOAT_LEFT_FRONT_TOP	               "LadarFloatLeftFrontTop"
#define RCS_SYNC_LIDAR_DATA	                           "SyncLidarData"
#define RCS_RADAR_MIDDLE_FRONT_BOTTOM	               "RadarMiddleFrontBottom"
#define RCS_RADAR_RIGHT_FRONT_BOTTOM	               "RadarRightFrontBottom"
#define RCS_RADAR_RIGHT_MIDDLE_BOTTOM	               "RadarRightMiddleBottom"
#define RCS_RADAR_RIGHT_REAR_BOTTOM	               "RadarRightRearBottom"
#define RCS_RADAR_MIDDLE_REAR_BOTTOM	               "RadarMiddleRearBottom"
#define RCS_RADAR_LEFT_REAR_BOTTOM	                   "RadarLeftRearBottom"
#define RCS_RADAR_LEFT_MIDDLE_BOTTOM	               "RadarLeftMiddleBottom"
#define RCS_RADAR_LEFT_FRONT_BOTTOM	               "RadarLeftFrontBottom"
#define RCS_CAMERA_MIDDLE_FRONT_LONG	               "CameraMiddleFrontLong"
#define RCS_CAMERA_MIDDLE_FRONT_SHORT	               "CameraMiddleFrontShort"
#define RCS_CAMERA_MIDDLE_FRONT_SPECIAL	           "CameraMiddleFrontSpecial"
#define RCS_CAMERA_RIGHT_MIDDLE	                   "CameraRightMiddle"
#define RCS_CAMERA_MIDDLE_REAR	                       "CameraMiddleRear"
#define RCS_CAMERA_LEFT_MIDDLE	                       "CameraLeftMiddle"
#define RCS_STEREO_MIDDLE_FRONT	                   "StereoMiddleFront"
#define RCS_STEREO_RIGHT_MIDDLE	                   "StereoRightMiddle"
#define RCS_STEREO_MIDDLE_REAR	                       "StereoMiddleRear"
#define RCS_STEREO_LEFT_MIDDLE  	                   "StereoLeftMiddle"

#define TAG_Group_Weather                               "天气相关"
#define TAG_Group_Environment                           "环境相关"
#define TAG_Group_Map                                   "地图相关"
#define TAG_Group_Plan                                  "规划相关"
#define TAG_Group_Road                                  "道路相关"

#define TAG_Value_Rain                                  "大雨"
#define TAG_Value_Snow                                  "大雪"
#define TAG_Value_Sun                                   "强烈阳光"
#define TAG_Value_Dusky                                 "灯光很暗"
#define TAG_Value_Crowds                                "人流量大"
#define TAG_Value_Curve                                 "弯道"
#define TAG_Value_Crossroads                            "十字路口"
#define TAG_Value_DownHill                              "连续下坡"
#define TAG_Value_Station                               "停靠点异常"
#define TAG_Value_Format                                "版本错误"
#define TAG_Value_Brake                                 "急刹"

#define TAG_Source_Daq                                  "采集系统"


namespace xsdaq {
#define MAX_MESSAGE_LENTH 1472

    struct ByteBuff : std::vector<uint8_t> {
        ByteBuff(): _wp(0), _rp(0) {
            reserve(MAX_MESSAGE_LENTH);
        }

        ByteBuff(const uint8_t *first, const uint8_t *last): vector(first, last) {
        }

        const uint8_t *rp() const noexcept { return (this->data() + _rp); }
        uint8_t *wp() noexcept { return (this->data() + _wp); }

        size_t _wp = 0, _rp = 0;
    };

    template<class T>
    ByteBuff &operator<<(ByteBuff &bytebuff, const T &val) {
        static_assert(std::is_scalar<T>::value || std::is_enum<T>::value, "No support nonscalar or pointer type");
        if (std::is_enum<T>::value) {
            using fixed_enum = unsigned short;
            fixed_enum fixed_val = val;
            bytebuff.resize(bytebuff.size() + sizeof(fixed_enum));
            *((fixed_enum *) (bytebuff.wp())) = fixed_val;
            bytebuff._wp += sizeof(fixed_enum);
        } else {
            bytebuff.resize(bytebuff.size() + sizeof(T));
            *((T *) (bytebuff.wp())) = val;
            bytebuff._wp += sizeof(T);
        }
        return bytebuff;
    }

    template<class T>
    ByteBuff &operator >>(ByteBuff &bytebuff, T &val) {
        static_assert(std::is_scalar<T>::value, "No support nonscalar or pointer type");
        if (std::is_enum<T>::value) {
            using fixed_enum = unsigned short;
            val = T(*((fixed_enum *) (bytebuff.rp())));
            bytebuff._rp += sizeof(fixed_enum);
        } else {
            val = *((T *) (bytebuff.rp()));
            bytebuff._rp += sizeof(T);
        }
        return bytebuff;
    }

    template<class T>
    ByteBuff &operator<<(ByteBuff &bytebuff, const std::vector<T> &vec) {
        size_t len = vec.size();
        bytebuff << len;
        if (0 < len) {
            for (size_t i = 0; i < len; ++i)bytebuff << vec[i];
        }
        return bytebuff;
    }

    template<class T>
    ByteBuff &operator>>(ByteBuff &bytebuff, std::vector<T> &vec) {
        size_t len = 0;
        bytebuff >> len;
        if (0 < len) {
            vec.resize(len);
            for (size_t i = 0; i < len; ++i)bytebuff >> vec[i];
        }
        return bytebuff;
    }

    template<class T>
    ByteBuff &operator<<(ByteBuff &bytebuff, const std::list<T> &list) {
        size_t len = list.size();
        bytebuff << len;
        if (0 < len) {
            for (auto it = list.begin(); it != list.end(); ++it)bytebuff << *it;
        }
        return bytebuff;
    }

    template<class T>
    ByteBuff &operator>>(ByteBuff &bytebuff, std::list<T> &list) {
        size_t len = 0;
        bytebuff >> len;
        if (0 < len) {
            for (size_t i = 0; i < len; ++i) {
                list.emplace_back(T());
                bytebuff >> list.back();
            }
        }
        return bytebuff;
    }

    template<>
    inline ByteBuff &operator<<(ByteBuff &bytebuff, const std::string &val) {
        size_t len = val.length();
        bytebuff << len;
        if (0 < len) {
            bytebuff.resize(bytebuff.size() + len);
            memcpy(bytebuff.wp(), val.c_str(), len);
            bytebuff._wp += len;
        }
        return bytebuff;
    }

    template<>
    inline ByteBuff &operator>>(ByteBuff &bytebuff, std::string &val) {
        val.clear();
        size_t length = 0;
        bytebuff >> length;
        if (0 < length) {
            val.resize(length);
            memcpy((void *) (val.data()), bytebuff.rp(), length);
            bytebuff._rp += length;
        }
        return bytebuff;
    }


    using ProtocalPtr = std::shared_ptr<xsproto::XSProtocol>;
    using SocketPtr = std::shared_ptr<xsnetwork::UdpSocket>;

    enum ErrorCode {
        SUCC = 0,
        TIMEOUT,
        SUSPEND,
        PEER_REJECT,
        NETWORK_REJECT,
        UNKOWN,
    };

    enum MessageIdentifier {
        MID_ECHO = 0,
        MID_CONNECT_RQST = 1,
        MID_CONNECT_RESP = 2,
        MID_START_TASK_RQST = 3,
        MID_START_TASK_RESP = 4,
        MID_STOP_TASK_RQST = 5,
        MID_STOP_TASK_RESP = 6,
        MID_REPORT_CHANELL_STATE_RQST = 7,
        MID_REPORT_CHANELL_STATE_RESP = 8,
        MID_REPORT_TASK_STATE_RQST = 9,
        MID_REPORT_TASK_STATE_RESP = 10,
        MID_INTERNAL_STOP_TASK_RQST = 11,
        MID_INTERNAL_STOP_TASK_RESP = 12,
        MID_STOP_WORKER_RQST = 13,
        MID_STOP_WORKER_RESP = 14,
        //*****************cheng*****************
        MID_CH_MSG_RQST = 15,
        MID_CH_MSG_RESP = 16,
        MID_SYS_INFO_RQST = 17,
        MID_SYS_INFO_RESP = 18,
        MID_PACKET_INFO_RQST = 19,
        MID_PACKET_INFO_RESP = 20,
        MID_REPORT_PACKET_RQST = 21,
        MID_REPORT_PACKET_RESP = 22,
        MID_TASK_INFO_RQST = 23,
        MID_TASK_INFO_RESP = 24,
        MID_TASK_CHANGE_RQST = 25,
        MID_TASK_CHANGE_RESP = 26,
        MID_STOPED_TASKS_RQST = 27,
        MID_STOPED_TASKS_RESP = 28,
        MID_TASK_INFO_NOTIFY = 29,
        MID_PACKET_INFO_NOTIFY = 30,
        MID_MAX
    };


    class XSProtoSocket : public xsproto::XSProtocol, public xsnetwork::UdpSocket {
    public:
        XSProtoSocket(MessgeHandler hander): xsproto::XSProtocol(hander) {
        }

        int send(int32_t msg_id, const uint8_t *data, int32_t len, xsnetwork::HostAddress &recv_addr,
                 unsigned short recv_port) noexcept {
            const xsproto::XSProtocol::ByteBuff &buf_send = encode(data, len, msg_id, 0, 0, 0, 0);
            return writeDatagram((const char *) buf_send.data(), buf_send.size(), recv_addr, recv_port);
        }
    };

    namespace msg {
        struct Error {
            int32_t _err_code;
            std::string _err_text;

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const Error &err_msg) {
                bytebuff << err_msg._err_code;
                bytebuff << err_msg._err_text;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, Error &err_msg) {
                bytebuff >> err_msg._err_code;
                bytebuff >> err_msg._err_text;
                return bytebuff;
            }
        };

        struct TaskError : public Error {
            int _task_id;

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const TaskError &err_msg) {
                bytebuff << static_cast<const Error &>(err_msg);
                bytebuff << err_msg._task_id;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, TaskError &err_msg) {
                bytebuff >> static_cast<Error &>(err_msg);
                bytebuff >> err_msg._task_id;
                return bytebuff;
            }
        };

        class Task {
        public:
            enum StorageMode {
                //自动存储:采集任务在一个预设置时间段内完成并且存储
                //注意：当设置此项时，需要设置使用setDuration方法设置采集任务的采集时间
                SM_AUTO_STOP = 0,
                //手动存储:需要任务发起者手动触发停止采集任务
                SM_MANUAL_STOP
            };

            enum CompressionMode {
                CM_NULL = 0, //本次采集任务不开启数据压缩,注意当使用此项后,压缩格式字段不再使用使用
                CM_REAL_TIME, //本次采集任务开启实时压缩即每次获取到数据都执行压缩
                CM_AT_STORAGE, //本次采集任务在结束时对数据进行压缩后再存储
            };

            enum CompressionFormat {
                CF_NULL = 0, //无压缩格式，注意当使用CM_NULL时,压缩格式字段不再使用
                CF_ZIP, //ZIP压缩格式
                GF_GZ //GZ压缩格式
            };

            enum StoreMode {
                ST_LOCAL = 0, //本地磁盘存储
                ST_CLOUD //云端存储
            };

            Task(unsigned short priority = 0,
                 unsigned short frequency = 100,
                 StorageMode store_mode = SM_AUTO_STOP,
                 CompressionMode comp_mode = CM_NULL,
                 CompressionFormat comp_format = CF_NULL,
                 const std::string &store_path = std::string(),
                 const std::string &text = std::string(),
                 StoreMode sto_mode = ST_LOCAL): _priority(priority),
                                                 _frequency(frequency),
                                                 _sm(store_mode),
                                                 _cm(comp_mode),
                                                 _cf(comp_format),
                                                 _store_path(store_path),
                                                 _text(text),
                                                 _store_mode(sto_mode) {
            }

            inline unsigned short priority() const noexcept { return _priority; }
            inline void setPriority(unsigned short priority) noexcept { _priority = priority; }

            inline unsigned short frequency() const noexcept { return _frequency; }
            void setFrequency(unsigned short frequency) noexcept { _frequency = frequency; }

            inline const std::string &storagePath() const noexcept { return _store_path; }
            inline void setStoragePath(const std::string &path) { _store_path = path; }

            inline StorageMode storageMode() const noexcept { return _sm; }
            inline void setStorageMode(StorageMode mode) noexcept { _sm = mode; }

            inline CompressionMode compressionMode() const noexcept { return _cm; }
            void setCompressionMode(CompressionMode mode) noexcept { _cm = mode; }

            inline CompressionFormat compressionFormat() const noexcept { return _cf; }
            inline void setCompressionFormat(CompressionFormat cf) noexcept { _cf = cf; }

            inline const std::string &text() const noexcept { return _text; }
            inline void setText(const std::string &text) noexcept { _text = text; }

            inline int duration() const noexcept { return _duration; }
            inline void setDuration(unsigned int seconds) noexcept { _duration = seconds; }

            void addTag(const std::string &group, const std::string &val, const std::string &src) {
                std::string tagstr = "group=" + group + ",val=" + val + ",src=" + src;
                _tags.push_back(tagstr);
            }

            void cleanTags() { _tags.clear(); }
            const std::vector<std::string> &tags() const noexcept { return _tags; }

            void addDataSource(const std::string &source) {
                _data_srcs.push_back(source);
            }

            const std::vector<std::string> &dataSources() const noexcept { return _data_srcs; }

            inline const std::string &clientId() const noexcept { return _client_id; }
            void setClientId(const std::string &id) noexcept { _client_id = id; }

            inline const std::string &taskName() const noexcept { return _task_name; }
            void setTaskName(const std::string &name) noexcept { _task_name = name; }

            inline void setTaskId(int task_id) noexcept { _task_id = task_id; }
            int taskId() const noexcept { return _task_id; }

            inline void setTaskSource(const std::string &source) noexcept { _task_source = source; }
            std::string taskSource() const noexcept { return _task_source; }

            inline void setPacketPath(const std::string &path) noexcept { _packet_path = path; }
            std::string packetPath() const noexcept { return _packet_path; }

            inline void setWallStartCloc(const uint64_t &cloc) noexcept { _wall_start_cloc = cloc; }
            uint64_t wallStartCloc() const noexcept { return _wall_start_cloc; }

            inline void setCpuStartCloc(const uint64_t &cloc) noexcept { _cpu_start_cloc = cloc; }
            uint64_t cpuStartCloc() const noexcept { return _cpu_start_cloc; }

            inline void setStoreMode(const StoreMode &mode) noexcept { _store_mode = mode; }
            StoreMode storeMode() const noexcept { return _store_mode; }

            inline void setDonePacket(const std::string &path) noexcept { _done_packet_path = path; }
            std::string donePacket() const noexcept { return _done_packet_path; }

            //是否触发保存数据，true，记录触发事件且保存数据；false，只记录触发事件不保存数据
            inline void setTriggerWriteState(const bool &write) noexcept { _trigger_write = write; }
            bool TriggerWriteState() const noexcept { return _trigger_write; }

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const Task &task) {
                bytebuff << task._priority;
                bytebuff << task._frequency;
                bytebuff << task._sm;
                bytebuff << task._cm;
                bytebuff << task._cf;
                bytebuff << task._store_path;
                bytebuff << task._text;
                bytebuff << task._duration;
                bytebuff << task._tags;
                bytebuff << task._client_id;
                bytebuff << task._task_name;
                bytebuff << task._data_srcs;
                bytebuff << task._task_id;
                bytebuff << task._task_source;
                bytebuff << task._packet_path;
                bytebuff << task._wall_start_cloc;
                bytebuff << task._cpu_start_cloc;
                bytebuff << task._store_mode;
                bytebuff << task._done_packet_path;
                bytebuff << task._trigger_write;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, Task &task) {
                bytebuff >> task._priority;
                bytebuff >> task._frequency;
                bytebuff >> task._sm;
                bytebuff >> task._cm;
                bytebuff >> task._cf;
                bytebuff >> task._store_path;
                bytebuff >> task._text;
                bytebuff >> task._duration;
                bytebuff >> task._tags;
                bytebuff >> task._client_id;
                bytebuff >> task._task_name;
                bytebuff >> task._data_srcs;
                bytebuff >> task._task_id;
                bytebuff >> task._task_source;
                bytebuff >> task._packet_path;
                bytebuff >> task._wall_start_cloc;
                bytebuff >> task._cpu_start_cloc;
                bytebuff >> task._store_mode;
                bytebuff >> task._done_packet_path;
                bytebuff >> task._trigger_write;
                return bytebuff;
            }

        private:
            unsigned short _priority; //采集任务的优先级
            unsigned short _frequency; //采集任务的采集频率(赫兹)
            StorageMode _sm; //存储模式
            CompressionMode _cm; //压缩模式
            CompressionFormat _cf; //压缩格式
            std::string _store_path; //采集任务完成后数据存储的路径
            std::string _text; //备注文本
            int _duration = 0; //持续时间(秒)
            std::vector<std::string> _tags; //数据标签
            std::string _client_id; //发起任务的客户端标识符
            std::string _task_name; //本次采集任务的名称
            std::vector<std::string> _data_srcs; //本次采集任务的目标
            int _task_id; //本次采集任务的ID,由采集系统自动分配
            std::string _task_source; //本次采集任务的发起源
            std::string _packet_path; //写入数据的数据包路径
            uint64_t _wall_start_cloc; //开始采集挂钟时间ms
            uint64_t _cpu_start_cloc; //开始采集系统内部时间ms
            StoreMode _store_mode; //存储模式
            std::string _done_packet_path; //采集任务完成后，采集数据所在数据包路径
            bool _trigger_write = true; //是否触发保存数据，true，记录触发事件且保存数据；false，只记录触发事件不保存数据
        };


        struct ChannelState {
            std::string ch_name; //通道名
            std::string category; //分类
            int max_size; //最大帧大小
            int min_size; //最小帧大小
            int avg_size; //平均帧大小
            int max_freq; //最大输出频率
            int min_freq; //最小输出频率
            int avg_freq; //平均输出频率
            int rate; //数据流量

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const ChannelState &channel_stat) {
                bytebuff << channel_stat.ch_name;
                bytebuff << channel_stat.category;
                bytebuff << channel_stat.max_size;
                bytebuff << channel_stat.min_size;
                bytebuff << channel_stat.avg_size;
                bytebuff << channel_stat.max_freq;
                bytebuff << channel_stat.min_freq;
                bytebuff << channel_stat.avg_freq;
                bytebuff << channel_stat.rate;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, ChannelState &channel_stat) {
                bytebuff >> channel_stat.ch_name;
                bytebuff >> channel_stat.category;
                bytebuff >> channel_stat.max_size;
                bytebuff >> channel_stat.min_size;
                bytebuff >> channel_stat.avg_size;
                bytebuff >> channel_stat.max_freq;
                bytebuff >> channel_stat.min_freq;
                bytebuff >> channel_stat.avg_freq;
                bytebuff >> channel_stat.rate;
                return bytebuff;
            }
        };

        struct TaskState {
            int task_id; //任务id
            uint64_t first_tm; //第一次写入时间
            uint64_t end_tm; //最后一次写入时间
            int first_block_id; //第一次写入块id
            int first_frame; //第一次写入帧数
            uint64_t frame; //总帧数
            int last_block_id; //最后一次写入块id
            int last_frame; //最后一次写入帧数
            uint64_t size; //任务大小
            std::string store_path; //数据包存储路径
            std::string ch_name; //通道名

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const TaskState &task_state) {
                bytebuff << task_state.task_id;
                bytebuff << task_state.first_tm;
                bytebuff << task_state.end_tm;
                bytebuff << task_state.first_block_id;
                bytebuff << task_state.first_frame;
                bytebuff << task_state.frame;
                bytebuff << task_state.last_block_id;
                bytebuff << task_state.last_frame;
                bytebuff << task_state.size;
                bytebuff << task_state.store_path;
                bytebuff << task_state.ch_name;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, TaskState &err_msg) {
                bytebuff >> err_msg.task_id;
                bytebuff >> err_msg.first_tm;
                bytebuff >> err_msg.end_tm;
                bytebuff >> err_msg.first_block_id;
                bytebuff >> err_msg.first_frame;
                bytebuff >> err_msg.frame;
                bytebuff >> err_msg.last_block_id;
                bytebuff >> err_msg.last_frame;
                bytebuff >> err_msg.size;
                bytebuff >> err_msg.store_path;
                bytebuff >> err_msg.ch_name;
                return bytebuff;
            }
        };

        struct SysInfo {
            uint64_t _disk_size;
            uint64_t _used_disk_size;
            uint64_t _tmp_size;
            uint64_t _used_tmp_size;
            uint64_t _read_speed; //byte/s
            uint64_t _write_speed; //byte/s

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const SysInfo &sys_info) {
                bytebuff << sys_info._disk_size;
                bytebuff << sys_info._used_disk_size;
                bytebuff << sys_info._tmp_size;
                bytebuff << sys_info._used_tmp_size;
                bytebuff << sys_info._read_speed;
                bytebuff << sys_info._write_speed;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, SysInfo &sys_info) {
                bytebuff >> sys_info._disk_size;
                bytebuff >> sys_info._used_disk_size;
                bytebuff >> sys_info._tmp_size;
                bytebuff >> sys_info._used_tmp_size;
                bytebuff >> sys_info._read_speed;
                bytebuff >> sys_info._write_speed;
                return bytebuff;
            }
        };


        struct PacketInfo {
            std::string packet_name;
            std::vector<std::string> vec_channel; //采集目标
            uint64_t packet_size; //任务包大小
            std::string path; //任务包完整路径
            std::vector<xsdaq::msg::Task> task; //任务包包含的采集任务
            int task_num; //包含采集任务数量
            uint64_t cloc; //任务包开始时间

            friend ByteBuff &operator <<(ByteBuff &bytebuff, const PacketInfo &info) {
                bytebuff << info.packet_name;
                bytebuff << info.vec_channel;
                bytebuff << info.packet_size;
                bytebuff << info.path;
                bytebuff << info.task_num;
                bytebuff << info.task;
                bytebuff << info.cloc;
                return bytebuff;
            }

            friend ByteBuff &operator >>(ByteBuff &bytebuff, PacketInfo &info) {
                bytebuff >> info.packet_name;
                bytebuff >> info.vec_channel;
                bytebuff >> info.packet_size;
                bytebuff >> info.path;
                bytebuff >> info.task_num;
                bytebuff >> info.task;
                bytebuff >> info.cloc;
                return bytebuff;
            }
        };
    }

    //*********************************************message macros********************************************************************

#define _send_msg(rqst_msg_id, rqst_msg_seq, rqst_msg_body_ref, peer_addr, peer_port)  \
if (0 >= sendMessage(rqst_msg_id, rqst_msg_body_ref, peer_addr, peer_port, rqst_msg_seq)) {return NETWORK_REJECT;}

#define _send_nobody_msg(rqst_msg_id, rqst_msg_seq, peer_addr, peer_port)  \
if (0 >= sendMessage(rqst_msg_id, peer_addr, peer_port, rqst_msg_seq)) {return NETWORK_REJECT;}

#define _register_async_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, cb, msg_seq) \
if (0>=wait_time) { \
    if (cb) { \
        registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(\
            rsp_msg_id, [this,cb,msg_seq](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){ \
                if (nullptr != _cur_msg_header && _cur_msg_header->_msg_seq == msg_seq) {\
                    cb(msg);\
                }\
            }); \
    } \
    return SUSPEND; \
}

#define _register_async_nobodyrsp_cb(rsp_msg_id, wait_time, cb, msg_seq) \
if (0>=wait_time) { \
    if (cb) { \
        registerMessage(rsp_msg_id, [this,cb,msg_seq](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){ \
                if (nullptr != _cur_msg_header && _cur_msg_header->_msg_seq == msg_seq) {\
                    cb();\
                }\
            }); \
    } \
    return SUSPEND; \
}

#define _register_sync_nobodyrsp_cb(rsp_msg_id, wait_time, cb, msg_seq) \
bool get_flag = false;\
auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port){\
    if (nullptr != _cur_msg_header && _cur_msg_header->_msg_seq == msg_seq) {\
        get_flag = true; \
        if (nullptr != rsp_msg_body_ptr) *rsp_msg_body_ptr = msg; \
        on_get_rsp \
    }\
};\
registerMessage(rsp_msg_id,f); \
while (!get_flag && 0 < wait_time--) { if (0 >= processMessages())usleep(1000);}

#define _register_sync_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, on_get_rsp, msg_seq) \
bool get_flag = false;\
auto f = [&](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, std::remove_pointer<decltype (rsp_msg_body_ptr)>::type &msg){\
    if (nullptr != _cur_msg_header && _cur_msg_header->_msg_seq == msg_seq) {\
        get_flag = true; \
        if (nullptr != rsp_msg_body_ptr) *rsp_msg_body_ptr = msg; \
        on_get_rsp \
    }\
};\
registerMessage<std::remove_pointer<decltype (rsp_msg_body_ptr)>::type>(rsp_msg_id,f); \
while (!get_flag && 0 < wait_time--) { if (0 >= processMessages())usleep(1000);}


#define get_rsp_msg() msg
    //---request has body, response has body
#define register_rsp_cb(rqst_msg_id, rqst_msg_body_ref, rsp_msg_id, rsp_msg_body_ptr, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
do {\
unsigned int msg_seq = current_message_sequence();\
_send_msg(rqst_msg_id, msg_seq, rqst_msg_body_ref, peer_addr, peer_port) \
_register_async_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, cb, msg_seq) \
_register_sync_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, on_get_rsp, msg_seq) \
}while(false)

    //---request no body, response has body
#define register_rqstnobody_cb(rqst_msg_id, rsp_msg_id, rsp_msg_body_ptr, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
do {\
unsigned int msg_seq = current_message_sequence();\
_send_nobody_msg(rqst_msg_id, msg_seq, peer_addr, peer_port) \
_register_async_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, cb, msg_seq) \
_register_sync_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, on_get_rsp, msg_seq) \
}while(false)

    //---request has body, response no body
#define register_respnobody_cb(rqst_msg_id, rqst_msg_body_ref, rsp_msg_id, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
do {\
unsigned int msg_seq = current_message_sequence();\
_send_msg(rqst_msg_id, msg_seq, rqst_msg_body_ref, peer_addr, peer_port) \
_register_async_nobodyrsp_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, cb, msg_seq) \
_register_sync_nobodyrsp_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, on_get_rsp, msg_seq) \
}while(false)

    //---request no body, response no body
#define register_rqstnobody_respnobody_cb(rqst_msg_id, rsp_msg_id, peer_addr, peer_port, wait_time, cb, on_get_rsp) \
do {\
unsigned int msg_seq = current_message_sequence();\
_send_nobody_msg(rqst_msg_id, msg_seq, rqst_msg_body_ref, peer_addr, peer_port) \
_register_async_nobodyrsp_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, cb, msg_seq) \
_register_sync_nobodyrsp_cb(rsp_msg_id, rsp_msg_body_ptr, wait_time, on_get_rsp, msg_seq) \
}while(false)

#define bind_memfuc_cb(mem_func) \
    std::bind(&std::remove_pointer<decltype(this)>::type::mem_func, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3)

#define bind_no_param_memfuc_cb(mem_func) \
    std::bind(&std::remove_pointer<decltype(this)>::type::mem_func, this, std::placeholders::_1, std::placeholders::_2)

    /**************************************************NetworkMessageDispatcher*********************************************/
#define MSG_ID_CYCLE 1800000000u

    static unsigned int current_message_sequence() {
        using namespace std::chrono;
        std::chrono::microseconds cur_ms = std::chrono::duration_cast<std::chrono::microseconds>(
            system_clock::now().time_since_epoch());
        return cur_ms.count() % MSG_ID_CYCLE;
    }

    class NetworkMessageDispatcher {
    public:
        NetworkMessageDispatcher() {
            xsproto::XSProtocol::MessgeHandler f = std::bind(&NetworkMessageDispatcher::dealwith, this,
                                                             std::placeholders::_1, std::placeholders::_2);
            _socket = std::make_shared<XSProtoSocket>(f);
            _socket->setNonblock();
        }

        static int32_t generate_message_id();

        int processMessages() noexcept {
            if (nullptr == _socket) {
                return -1;
            }
            int message_count = 0;
            while (true) {
                if (!_socket->hasPendingDatagrams())
                    break;
                ++message_count;
                int readn = _socket->readDatagram(_buff, sizeof(_buff), _addr_sender, _port_sender);
                //                        std::cout << "Read Data" << std::endl;
                //                        std::cout << "==Peer Ip:" << _addr_sender.toDottedDecimal() << std::endl;
                //                        std::cout << "==Peer Port:" << _port_sender << std::endl;
                //                        std::cout << "==Raw Data Length::" << readn << std::endl;
                _socket->decode((const uint8_t *) _buff, readn);
                _socket->clearRecvBuff();
                //            if (once_flag)
                //                break;
                //            std::cout << "**************************************************************"  <<std::endl;
            }
            return message_count;
        }

        void dealwith(const xsproto::XSProtocolHeader *header, const uint8_t *payload) noexcept {
            //                std::cout << "==Message Sequence:" << header->_msg_seq << std::endl;
            //                std::cout << "==Message Type:" << header->_msg_type << std::endl;
            //                std::cout << "==Message Length:" << header->_msg_len << std::endl;
            //                std::cout << "==Message Payload:" << payload << std::endl;
            if (!(header->_msg_type < MID_MAX)) {
                std::cout << "Message Type too large:" << header->_msg_type << std::endl;
                return;
            }
            //        std::cout << std::boolalpha << bool(_handers[header->_msg_type]) << std::endl;
            if (_handers[header->_msg_type]) {
                //            std::cout << "process msg" << std::endl;
                _handers[header->_msg_type](header, payload);
            }
        }


        int sendMessage(int32_t msg_id, const xsnetwork::HostAddress &recv_addr, unsigned short recv_port,
                        unsigned int msg_seq = 0) noexcept {
            const xsproto::XSProtocol::ByteBuff &buf_send = _socket->encode(msg_id, 0,
                                                                            (0 != msg_seq
                                                                                 ? msg_seq
                                                                                 : nullptr == _cur_msg_header
                                                                                         ? 0
                                                                                         : _cur_msg_header->_msg_seq));
            return _socket->writeDatagram((const char *) buf_send.data(), buf_send.size(), recv_addr, recv_port);
        }

        template<class T>
        int sendMessage(int32_t msg_id, const T &msg_content, const xsnetwork::HostAddress &recv_addr,
                        unsigned short recv_port, unsigned int msg_seq = 0u) noexcept {
            ByteBuff buff;
            size_t msg_type_hash = typeid(T).hash_code();
            buff << msg_type_hash;
            buff << msg_content;
            const xsproto::XSProtocol::ByteBuff &buf_send = _socket->encode(buff.data(), buff.size(), msg_id, 0,
                                                                            (0 != msg_seq
                                                                                 ? msg_seq
                                                                                 : nullptr == _cur_msg_header
                                                                                         ? 0
                                                                                         : _cur_msg_header->_msg_seq));
            return _socket->writeDatagram((const char *) buf_send.data(), buf_send.size(), recv_addr, recv_port);
        }

        void registerMessage(unsigned short message_id,
                             std::function<void(const xsnetwork::HostAddress &sender_addr,
                                                unsigned short sender_port)> callback) {
            _handers[message_id] = [=](const xsproto::XSProtocolHeader *header, const uint8_t *payload) {
                _cur_msg_header = header;
                if (nullptr == _cur_msg_header) return;
                callback(_addr_sender, _port_sender);
                _cur_msg_header = nullptr;
            };
        }

        template<class T>
        void registerMessage(unsigned short message_id,
                             std::function<void(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                                                T &msg)> callback) {
            using MessageFunc = std::function<void(const xsproto::XSProtocolHeader *header, const T &msg)>;
            _handers[message_id] = [=](const xsproto::XSProtocolHeader *header, const uint8_t *payload) {
                _cur_msg_header = header;
                if (nullptr == _cur_msg_header || nullptr == payload) {
                    _cur_msg_header = nullptr;
                    std::cout << "[ERROR] Message Length Error For ID[" << message_id << "]" << std::endl;
                    return;
                }
                ByteBuff buff(payload + 0, payload + header->_msg_len);
                size_t msg_type_hash;
                buff >> msg_type_hash;
                if (typeid(T).hash_code() == msg_type_hash) {
                    std::shared_ptr<T> msg = std::make_shared<T>();
                    buff >> *msg;
                    callback(_addr_sender, _port_sender, *msg);
                } else {
                    std::cout << "[ERROR] Message Type Error For ID[" << message_id << "]" << std::endl;
                }

                _cur_msg_header = nullptr;
            };
        }

        void unresigserMessage(unsigned short message_id) noexcept {
            if (0 <= message_id && message_id < MID_MAX) {
                _handers[message_id] = xsproto::XSProtocol::MessgeHandler();
            }
        }

    protected:
        std::shared_ptr<XSProtoSocket> _socket;

    private:
        xsnetwork::HostAddress _addr_sender;
        unsigned short _port_sender;
        char _buff[65536];
        std::array<xsproto::XSProtocol::MessgeHandler, MID_MAX> _handers;

    protected:
        const xsproto::XSProtocolHeader *_cur_msg_header = nullptr;
    };


#define DEFAULT_SERVER_ADDR ("0.0.0.0")
#define DEFAULT_HOST_ADDR ("**************")
#define DEFAULT_SERVER_PORT (50316)

    /************************************************worker**************************************************************/
    class BaseClient : public NetworkMessageDispatcher {
    public:
        BaseClient(): _server_addr(DEFAULT_SERVER_ADDR), _server_port(DEFAULT_SERVER_PORT) {
        }

        using EchoCallback = std::function<void(const std::string &str)>;

        ErrorCode echo(const std::string &text, std::string *echo_text = nullptr, int64_t millseconds = 0,
                       EchoCallback cb = EchoCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rsp_cb(xsdaq::MID_ECHO, text, xsdaq::MID_ECHO, echo_text, _server_addr, _server_port, millseconds,
                            cb, {
                            err = SUCC;
                            });
            return err;
        }

        inline void setServerHostAddress(const std::string &host_addr) {
            _server_addr = xsnetwork::HostAddress(host_addr);
        }

        const xsnetwork::HostAddress &serverHostAddress() const noexcept { return _server_addr; }

        inline void setServerPort(unsigned short port) { _server_port = port; }
        inline unsigned short serverPort() const noexcept { return _server_port; }

    protected:
        xsnetwork::HostAddress _server_addr;
        unsigned short _server_port;
    };

    class Worker : public BaseClient {
    public:
        Worker(const std::string &hash_code): _hash_code(hash_code) {
            //        registerMessage<xsdaq::msg::Task>(xsdaq::MID_START_TASK_RQST, bind_memfuc_cb(onStartTask));
            //        registerMessage<int>(xsdaq::MID_INTERNAL_STOP_TASK_RQST, bind_memfuc_cb(onStopTask));
            registerMessage<std::string>(xsdaq::MID_START_TASK_RQST, bind_memfuc_cb(onStartTask));
            registerMessage<std::string>(xsdaq::MID_INTERNAL_STOP_TASK_RQST, bind_memfuc_cb(onStopTask));
            registerMessage(xsdaq::MID_STOP_WORKER_RQST, bind_no_param_memfuc_cb(onStopWorker));
        }

        inline const std::string &hashCode() const noexcept { return _hash_code; }

        using ConnectCallback = std::function<void(const xsdaq::msg::Error &err_msg)>;

        ErrorCode connect(const std::string &worker_hash, xsdaq::msg::Error *resp = nullptr, int64_t millseconds = 0,
                          ConnectCallback cb = ConnectCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rsp_cb(xsdaq::MID_CONNECT_RQST, worker_hash, xsdaq::MID_CONNECT_RESP, resp, _server_addr,
                            _server_port, millseconds, cb, {
                            err = get_rsp_msg()._err_code == 0? SUCC: PEER_REJECT;
                            });
            return err;
        }

        ErrorCode sendChannelState(const xsdaq::msg::ChannelState &state) {
            return 0 < sendMessage(xsdaq::MID_REPORT_CHANELL_STATE_RQST, state, _server_addr, _server_port)
                       ? SUCC
                       : NETWORK_REJECT;
        }

        ErrorCode sendTaskState(const std::list<xsdaq::msg::TaskState> &state_list) {
            return 0 < sendMessage(xsdaq::MID_REPORT_TASK_STATE_RQST, state_list, _server_addr, _server_port)
                       ? SUCC
                       : NETWORK_REJECT;
        }

        ErrorCode sendPacket(const std::string &done_packet) {
            return 0 < sendMessage(xsdaq::MID_REPORT_PACKET_RQST, done_packet, _server_addr, _server_port)
                       ? SUCC
                       : NETWORK_REJECT;
        }

    protected:
        //    virtual void onStartTask(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, const xsdaq::msg::Task &task) noexcept = 0;
        //    virtual void onStopTask(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, const int &task_id) noexcept = 0;
        virtual void onStartTask(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                                 const std::string &buf) noexcept = 0;

        virtual void onStopTask(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                                const std::string &buf) noexcept = 0;

        virtual void onStopWorker(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port) noexcept = 0;

    private:
        std::string _hash_code;
    };

    //***********************************************client********************************************************/
    class Client : public BaseClient {
    public:
        Client(bool isModule = true) { if (!isModule)_server_addr.set(DEFAULT_HOST_ADDR); }

        /*
        **方法功能:启动采集任务
        **参数 task:
        **		对采集任务的详细描述 .
        **参数 millseconds:
        **		等待采集系统返回的时间(秒)
        **参数 err_msg:
        **		不为nullptr时,采集系统返回值填入其中.
        **返回值：
        **		1.如果返回TIMEOUT，说明采集系统没有在指定时间内给出返回值。
        **		2.如果返回SUCC，说明采集任务启动成功，否则采集任务启动失败，详细错误信息由err_msg给出
        */
        ErrorCode start(const xsdaq::msg::Task &task, int64_t millseconds,
                        xsdaq::msg::TaskError *err_msg = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return start(task, err_msg, millseconds);
        }

        /*
        **方法功能:启动采集任务
        **参数 task:
        **		对采集任务的详细描述 .
        **参数 cb:
        **		用者提供的回调函数,millseconds小于等于0时,采集系统的返回值将通过该回调传入.
        **返回值：
        **		返回SUSPEND，之后采集系统的返回值将通过回调函数(cb)传入.
        */
        using StartCallback = std::function<void(const xsdaq::msg::TaskError &err_msg)>;

        ErrorCode start(const xsdaq::msg::Task &task, StartCallback cb) noexcept {
            return start(task, nullptr, 0, cb);
        }

        /*
        **方法功能:启动采集任务
        **参数 task:
        **		对采集任务的详细描述 .
        **参数 err_msg:
        **		不为nullptr时且millseconds大于0,采集系统返回值填入其中.
        **参数 millseconds:
        **		等待采集系统返回的时间(秒)
        **参数 cb:
        **		用者提供的回调函数,millseconds小于等于0时,采集系统的返回值将通过该回调传入.
        **返回值：
        **		1.当参数 millseconds > 0 时，如果返回TIMEOUT，说明采集系统没有在指定时间内给出返回值。
        **		2.当参数 millseconds > 0 时，如果返回SUCC，说明采集任务启动成功，否则采集任务启动失败，详细错误信息由err_msg给出
        **		3.当参数 millseconds <= 0 时，返回SUSPEND，之后采集系统的返回值将通过回调函数(cb)传入.
        */

        ErrorCode start(const xsdaq::msg::Task &task, xsdaq::msg::TaskError *err_msg = nullptr, int64_t millseconds = 0,
                        StartCallback cb = StartCallback()) noexcept {
            xsdaq::ErrorCode err = TIMEOUT;
            register_rsp_cb(xsdaq::MID_START_TASK_RQST, task, xsdaq::MID_START_TASK_RESP, err_msg, _server_addr,
                            _server_port, millseconds, cb, {
                            err = get_rsp_msg()._err_code == 0? SUCC: PEER_REJECT;
                            });
            return err;
        }

        //功能：停止采集任务
        ErrorCode stop(const int &task_id, int64_t millseconds, xsdaq::msg::Error *err_msg = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return stop(task_id, err_msg, millseconds);
        }

        using stopCallback = std::function<void(const xsdaq::msg::Error &err_msg)>;

        ErrorCode stop(const int &task_id, stopCallback cb) noexcept {
            return stop(task_id, nullptr, 0, cb);
        }

        ErrorCode stop(const int &task_id, xsdaq::msg::Error *resp = nullptr, int64_t millseconds = 0,
                       stopCallback cb = stopCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rsp_cb(xsdaq::MID_STOP_TASK_RQST, task_id, xsdaq::MID_STOP_TASK_RESP, resp, _server_addr,
                            _server_port, millseconds, cb, {
                            err = get_rsp_msg()._err_code == 0? SUCC: PEER_REJECT;
                            });
            return err;
        }

        //功能：查询采集通道状态列表
        ErrorCode channelMsg(int64_t millseconds,
                             std::list<std::list<xsdaq::msg::ChannelState> > *msg_list = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return channelMsg(msg_list, millseconds);
        }

        using ChMsgCallback = std::function<void(const std::list<std::list<xsdaq::msg::ChannelState> > &msg_list)>;

        ErrorCode channelMsg(ChMsgCallback cb) noexcept {
            return channelMsg(nullptr, 0, cb);
        }

        ErrorCode channelMsg(std::list<std::list<xsdaq::msg::ChannelState> > *resp = nullptr, int64_t millseconds = 0,
                             ChMsgCallback cb = ChMsgCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rqstnobody_cb(xsdaq::MID_CH_MSG_RQST, xsdaq::MID_CH_MSG_RESP, resp, _server_addr, _server_port,
                                   millseconds, cb, {
                                   err = get_rsp_msg().size() != 0? SUCC: PEER_REJECT;
                                   });
            return err;
        }

        //查询系统状态信息
        ErrorCode systemInfo(int64_t millseconds, xsdaq::msg::SysInfo *msg = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return systemInfo(msg, millseconds);
        }

        using SysInfoCallback = std::function<void(const xsdaq::msg::SysInfo &msg)>;

        ErrorCode systemInfo(SysInfoCallback cb) noexcept {
            return systemInfo(nullptr, 0, cb);
        }

        ErrorCode systemInfo(xsdaq::msg::SysInfo *resp = nullptr, int64_t millseconds = 0,
                             SysInfoCallback cb = SysInfoCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rqstnobody_cb(xsdaq::MID_SYS_INFO_RQST, xsdaq::MID_SYS_INFO_RESP, resp, _server_addr, _server_port,
                                   millseconds, cb, {
                                   err = get_rsp_msg()._disk_size != 0? SUCC: PEER_REJECT;
                                   });
            return err;
        }

        //获取任务包状态
        ErrorCode packetInfo(int64_t millseconds, std::list<xsdaq::msg::PacketInfo> *msg_list = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return packetInfo(msg_list, millseconds);
        }

        using PacketCallback = std::function<void(const std::list<xsdaq::msg::PacketInfo> &msg_list)>;

        ErrorCode packetInfo(PacketCallback cb) noexcept {
            return packetInfo(nullptr, 0, cb);
        }

        ErrorCode packetInfo(std::list<xsdaq::msg::PacketInfo> *resp = nullptr, int64_t millseconds = 0,
                             PacketCallback cb = PacketCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rqstnobody_cb(xsdaq::MID_PACKET_INFO_RQST, xsdaq::MID_PACKET_INFO_RESP, resp, _server_addr,
                                   _server_port, millseconds, cb, {
                                   err = SUCC;
                                   });
            return err;
        }

        //查询正在采集任务状态列表
        ErrorCode taskInfo(int64_t millseconds, std::list<xsdaq::msg::Task> *msg_list = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return taskInfo(msg_list, millseconds);
        }

        using TaskInfoCallback = std::function<void(const std::list<xsdaq::msg::Task> &msg_list)>;

        ErrorCode taskInfo(TaskInfoCallback cb) noexcept {
            return taskInfo(nullptr, 0, cb);
        }

        ErrorCode taskInfo(std::list<xsdaq::msg::Task> *resp = nullptr, int64_t millseconds = 0,
                           TaskInfoCallback cb = TaskInfoCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rqstnobody_cb(xsdaq::MID_TASK_INFO_RQST, xsdaq::MID_TASK_INFO_RESP, resp, _server_addr,
                                   _server_port, millseconds, cb, {
                                   err = get_rsp_msg().size() != 0? SUCC: PEER_REJECT;
                                   });
            return err;
        }

        //修改任务内容
        ErrorCode changeTaskContent(const xsdaq::msg::Task &task, int64_t millseconds,
                                    xsdaq::msg::Error *err_msg = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return changeTaskContent(task, err_msg, millseconds);
        }

        using changeCallback = std::function<void(const xsdaq::msg::Error &err_msg)>;

        ErrorCode changeTaskContent(const xsdaq::msg::Task &task, changeCallback cb) noexcept {
            return changeTaskContent(task, nullptr, 0, cb);
        }

        ErrorCode changeTaskContent(const xsdaq::msg::Task &task, xsdaq::msg::Error *resp = nullptr,
                                    int64_t millseconds = 0, changeCallback cb = changeCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rsp_cb(xsdaq::MID_TASK_CHANGE_RQST, task, xsdaq::MID_TASK_CHANGE_RESP, resp, _server_addr,
                            _server_port, millseconds, cb, {
                            err = get_rsp_msg()._err_code == 0? SUCC: PEER_REJECT;
                            });
            return err;
        }

        //查询已完成任务
        ErrorCode stopedTasks(int64_t millseconds, std::list<xsdaq::msg::Task> *stoped_task = nullptr) noexcept {
            if (0 >= millseconds)
                return TIMEOUT;
            return stopedTasks(stoped_task, millseconds);
        }

        using stopedTaskCallback = std::function<void(const std::list<xsdaq::msg::Task> &stoped_task)>;

        ErrorCode stopedTasks(stopedTaskCallback cb) noexcept {
            return stopedTasks(nullptr, 0, cb);
        }

        ErrorCode stopedTasks(std::list<xsdaq::msg::Task> *resp = nullptr, int64_t millseconds = 0,
                              stopedTaskCallback cb = stopedTaskCallback()) noexcept {
            ErrorCode err = TIMEOUT;
            register_rqstnobody_cb(xsdaq::MID_STOPED_TASKS_RQST, xsdaq::MID_STOPED_TASKS_RESP, resp, _server_addr,
                                   _server_port, millseconds, cb, {
                                   err = SUCC;
                                   });
            return err;
        }

        //
        ErrorCode onTaskInfoNotify(stopedTaskCallback cb) {
            ErrorCode err = TIMEOUT;
            if (cb) {
                registerMessage<std::list<xsdaq::msg::Task> >(
                    xsdaq::MID_TASK_INFO_NOTIFY,
                    [this,cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                              std::list<xsdaq::msg::Task> &msg) {
                        cb(msg);
                    });
            }
            err = SUCC;
            return err;
        }

        ErrorCode onPacketInfoNotify(PacketCallback cb) {
            ErrorCode err = TIMEOUT;
            if (cb) {
                registerMessage<std::list<xsdaq::msg::PacketInfo> >(
                    xsdaq::MID_PACKET_INFO_NOTIFY,
                    [this,cb](const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                              std::list<xsdaq::msg::PacketInfo> &msg) {
                        cb(msg);
                    });
            }
            err = SUCC;
            return err;
        }
    };

    //****************************************************************Server**********************************************//
    class Server : public NetworkMessageDispatcher {
    public:
        struct ConnetedWorker {
            xsnetwork::HostAddress worker_addr;
            unsigned short worker_port;
            std::string worker_hash_code;

            ConnetedWorker(const xsnetwork::HostAddress &addr, unsigned short port,
                           const std::string &hash_code): worker_addr(addr), worker_port(port),
                                                          worker_hash_code(hash_code) {
            }

            ConnetedWorker(const xsnetwork::HostAddress &addr, unsigned short port): ConnetedWorker(
                addr, port, std::string()) {
            }

            friend bool operator ==(const ConnetedWorker &l, const ConnetedWorker &r) {
                return (l.worker_addr == r.worker_addr) && (l.worker_port == r.worker_port);
            }
        };

        using WorkerList = std::list<ConnetedWorker>;
        using ConnetedWorkerPtr = std::shared_ptr<ConnetedWorker>;

    public:
        using ServerSocketPtr = std::shared_ptr<XSProtoSocket>;

        Server(const std::string host_addr = std::string(DEFAULT_SERVER_ADDR), unsigned short port = 50316) {
            xsproto::XSProtocol::MessgeHandler f =
                    std::bind(&Server::dealwith, this, std::placeholders::_1, std::placeholders::_2);
            if (!_socket->bind(host_addr, port)) {
                _socket.reset();
                return;
            }
            registerMessage<std::string>(xsdaq::MID_ECHO,
                                         std::bind(&Server::onEcho, this, std::placeholders::_1, std::placeholders::_2,
                                                   std::placeholders::_3));
            registerMessage<std::string>(xsdaq::MID_CONNECT_RQST,
                                         std::bind(&Server::onWorkerConnect, this, std::placeholders::_1,
                                                   std::placeholders::_2, std::placeholders::_3));
        }

        bool isValid() const noexcept { return nullptr != _socket; }

        void stopBuffer(std::string buf) noexcept {
            for (WorkerList::iterator it = _workers.begin(), end = _workers.end(); it != end; ++it) {
                ConnetedWorker &worker = *it;
                sendMessage(xsdaq::MID_INTERNAL_STOP_TASK_RQST, buf, worker.worker_addr, worker.worker_port);
            }
        }

        void stopTask(int task_id) noexcept {
            for (WorkerList::iterator it = _workers.begin(), end = _workers.end(); it != end; ++it) {
                ConnetedWorker &worker = *it;
                sendMessage(xsdaq::MID_INTERNAL_STOP_TASK_RQST, task_id, worker.worker_addr, worker.worker_port);
            }
        }

        void stopAllWoker() noexcept {
            for (WorkerList::iterator it = _workers.begin(), end = _workers.end(); it != end; ++it) {
                ConnetedWorker &worker = *it;
                sendMessage(xsdaq::MID_STOP_WORKER_RQST, worker.worker_addr, worker.worker_port);
            }
        }

        const WorkerList &workers() const noexcept { return _workers; }
        WorkerList &workers() noexcept { return _workers; }

    protected:
        void onEcho(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port, const std::string &msg) {
            std::cout << "echo message=" << msg << std::endl;
            sendMessage(MID_ECHO, msg, sender_addr, sender_port);
        }

        void onWorkerConnect(const xsnetwork::HostAddress &sender_addr, unsigned short sender_port,
                             const std::string &worker_hash) {
            std::cout << "worker_hash=" << worker_hash << std::endl;
            xsdaq::msg::Error resp;
            resp._err_code = 0;
            resp._err_text = "SUCC";
            WorkerList::iterator found = findWorker(sender_addr, sender_port);
            if (_workers.end() != found) {
                resp._err_code = 1;
                resp._err_text = "already existed ";
            } else {
                pushWorker(sender_addr, sender_port, worker_hash);
            }
            sendMessage(MID_CONNECT_RESP, resp, sender_addr, sender_port);
        }

        void pushWorker(const xsnetwork::HostAddress &addr, unsigned short port,
                        const std::string &hash_code) noexcept {
            WorkerList::iterator found = std::find_if(_workers.begin(), _workers.end(),
                                                      [hash_code](const ConnetedWorker &worker) {
                                                          return worker.worker_hash_code == hash_code;
                                                      });
            if (_workers.end() == found) {
                _workers.emplace_back(ConnetedWorker(addr, port, hash_code));
            } else {
                found->worker_addr = addr;
                found->worker_port = port;
            }
        }

        WorkerList::iterator findWorker(const xsnetwork::HostAddress &addr, unsigned short port) noexcept {
            return std::find(_workers.begin(), _workers.end(), ConnetedWorker(addr, port));
        }

        WorkerList::const_iterator findWorker(const xsnetwork::HostAddress &addr, unsigned short port) const noexcept {
            return std::find(_workers.cbegin(), _workers.cend(), ConnetedWorker(addr, port));
        }

    protected:
        WorkerList _workers;
    };
}

#endif
