﻿#ifndef __PACKET_H__
#define __PACKET_H__


#include <stdint.h>
#include <vector>
#include <algorithm>
#include <functional>
#include <memory.h>

namespace xsproto {
#pragma pack(push)
#pragma pack(1)
    struct XSProtocolHeader {
        uint8_t _start_char;
        uint32_t _msg_seq;
        uint32_t _ack_seq;
        uint32_t _sock_id;
        uint32_t _msg_type;
        uint32_t _msg_len;
        uint16_t _ctl_flag;
        uint8_t _check_sum;
    };
#pragma pack(pop)

    class XSProtocol {
    public:
        using ByteBuff = std::vector<uint8_t>;
        using MessgeHandler = std::function<void(const XSProtocolHeader *header, const uint8_t *payload)>;

    private:
        ByteBuff _recv_buf, _send_buf;
        MessgeHandler _message_handler;

    public:
        enum { HeaderSize = sizeof(XSProtocolHeader) };

        static const XSProtocolHeader *getHeader(const ByteBuff &buf) {
            return HeaderSize <= buf.size() ? (XSProtocolHeader *) buf.data() : nullptr;
        }

        XSProtocol(MessgeHandler hander) : _message_handler(hander) {
        }

        int64_t findStartCharacter(int64_t offset = 0) const {
            const uint8_t *data = _recv_buf.data();
            for (size_t i = offset, size = _recv_buf.size(); i < size; ++i) {
                if (0x78 == data[i])
                    return i;
            }
            return -1;
        }

        void clearRecvBuff() {
            _recv_buf.clear();
        }

        void clearRecvBuff(int64_t index) {
            if (!(0 <= index && index < _recv_buf.size()))
                return;
            int64_t size_after_clear = _recv_buf.size() - index - 1;
            if (0 < size_after_clear)
                memmove(_recv_buf.data(), _recv_buf.data() + index + 1, size_after_clear);
            _recv_buf.resize(size_after_clear);
        }

        void appendToRecvBuff(const uint8_t *data, const int64_t length) {
            int64_t start_cy = _recv_buf.size();
            _recv_buf.resize(_recv_buf.size() + length);
            memcpy(_recv_buf.data() + start_cy, data, length);
        }

        void decode(const uint8_t *data, const int64_t length) {
            if (nullptr != data && 0 < length) {
                appendToRecvBuff(data, length);
            }

            int64_t index_offset = 0;
            do {
                bool flag_break = true;
            find_start:
                int64_t index_clear = _recv_buf.size() - 1, index_start = findStartCharacter(index_offset);

                if (0 <= index_start) {
                    index_clear = index_start - 1;

                    if ((_recv_buf.size() - index_start) >= HeaderSize) {
                        XSProtocolHeader *header = (XSProtocolHeader *) (_recv_buf.data() + index_start);
                        int64_t index_end = index_start + header->_msg_len + HeaderSize - 1;
                        if (index_end < _recv_buf.size()) {
                            flag_break = false;
                            if (verifyChecksum(index_start, index_end)) {
                                index_clear = index_end;
                                if (_message_handler) _message_handler(
                                    header, 0 < header->_msg_len ? (uint8_t *) (header + 1) : nullptr);
                            } else {
                                index_clear = index_start;
                            }
                        } else {
                            index_offset = index_start + 1;
                            goto find_start;
                        }
                    }
                }

                if (0 <= index_clear) {
                    clearRecvBuff(index_clear);
                }
                if (flag_break)
                    break;
            } while (HeaderSize < _recv_buf.size());
        }

        bool verifyChecksum(int64_t start, int64_t end) {
            if (!(0 <= start && start < _recv_buf.size()))
                return false;
            if (!(0 <= end && end < _recv_buf.size()))
                return false;
            uint8_t check_sum = 0;
            for (int i = start; i <= end; ++i)check_sum ^= _recv_buf[i];
            return 0 == check_sum;
        }

        const ByteBuff &encode(uint32_t msg_type, uint16_t ctl_flag = 0, uint32_t msg_seq = 0, uint32_t ack_seq = 0,
                               uint32_t sock_id = 0) {
            int buf_size = HeaderSize;
            if (buf_size != _send_buf.size())
                _send_buf.resize(buf_size);

            XSProtocolHeader *t = (XSProtocolHeader *) _send_buf.data();
            t->_start_char = 0x78;
            t->_msg_seq = msg_seq;
            t->_ack_seq = ack_seq;
            t->_sock_id = sock_id;
            t->_msg_type = msg_type;
            t->_msg_len = 0;
            t->_ctl_flag = ctl_flag;
            t->_check_sum = 0;
            uint8_t sum = 0;
            for (int64_t i = 0, size = _send_buf.size(); i < size; ++i) {
                sum ^= _send_buf[i];
            }
            t->_check_sum = sum;
            return _send_buf;
        }

        const ByteBuff &encode(const uint8_t *data, int64_t length, uint32_t msg_type = 0, uint16_t ctl_flag = 0,
                               uint32_t msg_seq = 0, uint32_t ack_seq = 0, uint32_t sock_id = 0) {
            int buf_size = HeaderSize + length;
            if (buf_size != _send_buf.size())
                _send_buf.resize(buf_size);

            XSProtocolHeader *t = (XSProtocolHeader *) _send_buf.data();
            t->_start_char = 0x78;
            t->_msg_seq = msg_seq;
            t->_ack_seq = ack_seq;
            t->_sock_id = sock_id;
            t->_msg_type = msg_type;
            t->_msg_len = length;
            t->_ctl_flag = ctl_flag;
            t->_check_sum = 0;
            memcpy(t + 1, data, length);
            uint8_t sum = 0;
            for (int64_t i = 0, size = _send_buf.size(); i < size; ++i) {
                sum ^= _send_buf[i];
            }
            t->_check_sum = sum;
            return _send_buf;
        }
    };
}
#endif
