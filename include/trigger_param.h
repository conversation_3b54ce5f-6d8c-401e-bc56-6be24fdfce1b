#ifndef TRIGGER_PARAM_H
#define TRIGGER_PARAM_H

#include <string>
#include <vector>
#include <memory>
#include "nlohmann/json.hpp"

namespace param {
    const std::string _CONFIG_FILE_ = std::string("parameters/") + std::string(PROCESS_NAME) + std::string(".json");
}

// 触发器类型枚举
enum class TriggerType {
    TRAFFIC_LIGHT_MISSING_IN_RANGE,
    TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE,
    TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE,
    TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW,
    OBJECT_DETECTION_INSTABILITY,
    OBSTACLE_DETECTION_SUDDEN_APPEARANCE
};

// 任务参数
struct TaskParam {
    std::string task_name;
    int duration;
    std::vector<std::string> data_sources;
    std::string task_source;
    std::string collect_scene;
    std::string collect_reason;
};

// 触发器参数基类
struct BaseTriggerParam {
    TriggerType type;
    TaskParam task_param;
    bool enabled;
    int duration;
    
    BaseTriggerParam(TriggerType t) : type(t), enabled(false), duration(0) {}
    virtual ~BaseTriggerParam() = default;
    
    // 类型安全的转换方法
    template<typename T>
    T* as() {
        return static_cast<T*>(this);
    }
    
    template<typename T>
    const T* as() const {
        return static_cast<const T*>(this);
    }
};

// 红绿灯触发参数
struct TrafficCollectParam {
    TaskParam task_param;
    bool enabled;
    int stopline_distance;
    int change_time;
};

// 感知触发参数
struct PerceptionTriggerParam {
    TaskParam task_param;
    bool enabled;
    float loss_time;
};


// 红绿灯类触发参数
struct TrafficLightMissingInRange : public BaseTriggerParam {
    int detection_range_threshold; // 检测范围阈值
    
    TrafficLightMissingInRange() : BaseTriggerParam(TriggerType::TRAFFIC_LIGHT_MISSING_IN_RANGE) {}
};

struct TrafficLightDetectedOutOfRange : public BaseTriggerParam {
    int false_detection_distance_threshold; // 误检距离阈值
    
    TrafficLightDetectedOutOfRange() : BaseTriggerParam(TriggerType::TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE) {}
};

struct TrafficLightIllogicalStateChange : public BaseTriggerParam {
    int distance_range_min; // 距离范围最小值
    int distance_range_max; // 距离范围最大值
    std::vector<std::string> jump_patterns; // 跳变模式
    
    TrafficLightIllogicalStateChange() : BaseTriggerParam(TriggerType::TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE) {}
};

struct TrafficLightViolateTrafficFlow : public BaseTriggerParam {
    int traffic_flow_average_speed_threshold; // 车流平均速度阈值
    
    TrafficLightViolateTrafficFlow() : BaseTriggerParam(TriggerType::TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW) {}
};

// 目标检测类触发参数
struct ObjectDetectionInstability : public BaseTriggerParam {
    int detection_distance_threshold; // 检测距离阈值
    
    ObjectDetectionInstability() : BaseTriggerParam(TriggerType::OBJECT_DETECTION_INSTABILITY) {}
};

// 障碍物检测类触发参数
struct ObstacleDetectionSuddenAppearance : public BaseTriggerParam {
    int obstacle_distance_threshold; // 障碍物距离阈值
    
    ObstacleDetectionSuddenAppearance() : BaseTriggerParam(TriggerType::OBSTACLE_DETECTION_SUDDEN_APPEARANCE) {}
};

// 统一的触发器参数容器
using TriggerParamPtr = std::unique_ptr<BaseTriggerParam>;
using TriggerParamList = std::vector<TriggerParamPtr>;

// 触发器工厂类
class TriggerParamFactory {
public:
    // 从JSON配置创建启用的触发器参数列表
    static TriggerParamList LoadConfig();
    
    // 根据类型创建具体的触发器参数
    static TriggerParamPtr CreateTrigger(TriggerType type);
    
    // 类型安全的触发器执行
    template<typename T, typename Func>
    static void ExecuteIfType(BaseTriggerParam* param, TriggerType target_type, Func&& func) {
        if (param && param->type == target_type && param->enabled) {
            func(param->as<T>());
        }
    }
    
    // 遍历执行所有启用的触发器
    template<typename Func>
    static void ForEachEnabled(const TriggerParamList& triggers, Func&& func) {
        for (const auto& trigger : triggers) {
            if (trigger && trigger->enabled) {
                func(trigger.get());
            }
        }
    }
    
    // 打印所有触发器的详细信息
    static void PrintAllTriggers(const TriggerParamList& triggers);
    
private:
    // 辅助函数：从JSON填充基础参数
    static void FillBaseParams(BaseTriggerParam* param, const nlohmann::json& config);
    
    // 辅助函数：从JSON填充TaskParam
    static void FillTaskParam(TaskParam& task_param, const nlohmann::json& config);
    
    // 辅助函数：从JSON填充特定参数
    static void FillSpecificParams(BaseTriggerParam* param, const nlohmann::json& config);
};

#endif
