


#ifndef UPD_SOCKET_HPP
#define UPD_SOCKET_HPP

#include <signal.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <poll.h>

#include <vector>
#include <string>
#include <cstring>

#ifdef __SOCK_DEBUG__
#	define print_err_string (perror(strerror(errno)))
#	else
#	define print_err_string
#endif

#define no_interrupt_call(var, func)                    \
    do {                                        \
        var = func;                              \
    } while (var == -1 && errno == EINTR)

    typedef int SOCK_FD;


#define if_nonzero_then_false(clause) do{\
    if (0!=clause){\
        result=false;\
        print_err_string;\
    }\
}while(false)

#define if_lesszero_then_err(clause) do{\
    if (clause < 0){\
        print_err_string;\
    }\
}while(false)

namespace xsnetwork {


union SocketAddress
{
    sockaddr a;
    sockaddr_in a4;
};


static bool bind_unix(int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    bool result = true;
    if_nonzero_then_false(::bind(sockfd, addr, addrlen));
    return result;
}

static int64_t send_to_unix(int sock_fd, const char* data, int64_t data_len, int flags, const struct sockaddr *dest_addr, socklen_t addr_len)
{
   ssize_t res;
   no_interrupt_call(res, ::sendto(sock_fd, data, data_len, flags, dest_addr, addr_len));
   if_lesszero_then_err(res);
   return res;
}

static int64_t recv_from_unix(int sock_fd, char* data, int64_t data_len, int flags, struct sockaddr *dest_addr, socklen_t *addr_len)
{
   ssize_t res;
   no_interrupt_call(res, ::recvfrom(sock_fd, data, data_len, flags, dest_addr, addr_len));
   if_lesszero_then_err(res);
   return res;
}


class HostAddress
{
public:
    enum Endian
    {
        HOST = 0,
        NETWORK,
    };

    static HostAddress fromNetworkEndian(uint32_t ip) {
        HostAddress res;
        res._ip.a4 = ntohl(ip);
        return res;
    }

    HostAddress():HostAddress(std::string()){
    }

    HostAddress(const std::string &ip){
        set(ip);
    }

    bool set(const std::string &ip){
        if (ip.empty()) {
            _ip.a4 = 0u;
        }
        else {
            if (1 != inet_pton(AF_INET, ip.c_str(), &_ip.a4)) return false;
            _ip.a4 = ntohl(_ip.a4);
        }
        return true;
    }
    uint32_t get(Endian e = HOST)const {
        uint32_t res = 0;
        if (NETWORK == e){
            res = htonl(_ip.a4);
        }
        else {
            res = _ip.a4;
        }
        return res;
    }
    std::string toDottedDecimal()const {
        std::string result;
        for (int i=3; i>=0; --i){
            result.append(std::to_string(_ip.a1[i]));
            result.push_back('.');
        }
        return result;
    }

    friend inline bool operator==(const HostAddress &l, const HostAddress &r) {
        return l._ip.a4 == r._ip.a4;
    }

private:
    union {
        uint8_t a1[4];
        uint32_t a4;
    }_ip;
};


#include <stdio.h>

class UdpSocket
{
public:
    UdpSocket()
    {
        _fd= socket(AF_INET, SOCK_DGRAM, 0);
        if_lesszero_then_err(_fd);
    }
    virtual ~UdpSocket()
    {
        close();
    }

    bool bind(const std::string &ip_addr, uint16_t port)
    {
        if(0 > _fd) {
            return false;
        }

        HostAddress bind_addr(ip_addr);
        sockaddr_in servaddr;
        bzero(&servaddr, sizeof(servaddr));
        servaddr.sin_family = AF_INET;
        servaddr.sin_port = htons(port);
        servaddr.sin_addr.s_addr = bind_addr.get(HostAddress::NETWORK);
        return bind_unix(_fd, (sockaddr*)&servaddr, sizeof(sockaddr_in));
    }
    bool setNonblock()
    {
        if (0 > _fd) {
            return false;
        }
        /* Set a socket as nonblocking */
        int flags;
        if ( (flags = fcntl (_fd, F_GETFL, 0)) < 0)
        {
            perror("F_GETFL error");
            return false;
        }

        flags |= O_NONBLOCK;
        if (fcntl(_fd, F_SETFL, flags) < 0)
        {
            perror("F_SETFL error");
            return false;
        }
        return true;
    }
//    bool joinMulticastGroup(const HostAddress &groupAddress,const HostAddress &hostAddress);
//    bool leaveMulticastGroup(const HostAddress &groupAddress,const HostAddress &hostAddress);
    bool hasPendingDatagrams() const
    {
        int64_t readBytes;
        do {
            char c;
            readBytes = ::recvfrom(_fd, &c, 1, MSG_PEEK, nullptr, nullptr);
        } while (readBytes == -1 && errno == EINTR);

        return (readBytes != -1) || errno == EMSGSIZE;
    }
    void close() {
        if (!(0>_fd)) {
            ::close(_fd);
        }
    }
    int64_t pendingDatagramSize()const
    {
        std::vector<char> udpMessagePeekBuffer;
        udpMessagePeekBuffer.resize(8192);

        int64_t recvResult = -1;
        for (;;) {
            recvResult = ::recv(_fd, udpMessagePeekBuffer.data(),
                                udpMessagePeekBuffer.size(), MSG_PEEK);
            if (recvResult == -1 && errno == EINTR)
                continue;

            if (recvResult != int64_t(udpMessagePeekBuffer.size()))
                break;

            udpMessagePeekBuffer.resize(udpMessagePeekBuffer.size() * 2);
        }
        return recvResult;
    }
    int64_t readDatagram(char *data, int64_t size, HostAddress &host, uint16_t &port)
    {
        SocketAddress addr;
        socklen_t addr_len = sizeof(addr.a);

        int64_t readn = recv_from_unix(_fd, data, size, 0, &addr.a, &addr_len);
        if (0 < readn) {
            port = ntohs(addr.a4.sin_port);
            host = HostAddress::fromNetworkEndian(addr.a4.sin_addr.s_addr);
        }
        return readn;
    }
    int64_t writeDatagram(const char *data, int64_t size, const HostAddress &address, const uint16_t port) const
    {
        SocketAddress addr;
        bzero(&addr.a4, sizeof(addr.a4));
        addr.a4.sin_family = AF_INET;
        addr.a4.sin_port = htons(port);
        addr.a4.sin_addr.s_addr = address.get(HostAddress::NETWORK);
        int64_t sendn = send_to_unix(_fd, data, size, 0, &addr.a, sizeof(addr.a));
        return sendn == size? sendn: -1;
    }
private:
    int _fd;
};





















}































#endif
