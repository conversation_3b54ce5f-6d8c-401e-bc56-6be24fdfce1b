#ifndef TRAFFICTRIGGER_H
#define TRAFFICTRIGGER_H
#include <iostream>
#include <thread>
#include <unistd.h>
#include <cmath>
#include <signal.h>
#include <time.h>
#include <atomic>

#include "unified_comm.h"

// RCS
#include "buffer_10kb.hh"
#include "buffer_2mb.hh"
// XSCOM
#include <xscom/xscom.h>
// XSProto
#include "common/common.h"
#include "nodedefine.h"
#include "channeldefine.h"

#include "base/local_pose.pb.h"
#include "hdmap/local_hdmap.pb.h"
#include "perception/traffic_light_info.pb.h"

//opencv
#include <opencv2/opencv.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>

using LocalPoseData = xsproto::base::LocalPose;
using LocalHdmapData = xsproto::hdmap::LocalHDMap;
using TrafficLightData = xsproto::perception::TrafficLightInfo;


class TrafficLightTrigger
{
public:
    TrafficLightTrigger();
    ~TrafficLightTrigger();

    int SearchData(double Val, std::deque<LocalHdmapData> data);
    int SearchData(double Val, std::deque<TrafficLightData> data);
    double calculateAngle(double x1, double y1, double x2, double y2);
    void timer_handler(int time_sec, bool flag);
    //逻辑判断函数
    bool StopLineDistanceLogic(UnifiedComm *data, TrafficCollectParam param);
    bool TrafficChangeLogic(UnifiedComm *data, TrafficCollectParam param);


private:
    //逻辑处理判断需要用到的数据
    LocalPoseData m_curr_lp;
    LocalHdmapData m_curr_hdmap;
    TrafficLightData m_curr_trafficlight;

    std::atomic<bool> m_stopline_distance_flag{false};
    std::atomic<bool> m_trafficlight_change_flag{false};

    int m_last_stopline_id = -1;
};

#endif
