#include "perception_trigger.h"

#include <cmath>
#include <deque>

#include "common/common.h"

namespace
{
constexpr double kTimeThreshold = 0.3;
constexpr float kROIymin = -30;
constexpr float kROIymax = 50;
constexpr float kROIxminmax = 20;
}  // namespace

PerceptionObjectInfoPtr PerceptionDataTrigger::GetLatestFusionInfo(std::deque<PerceptionObjectInfo> fused_que)
{
    PerceptionObjectInfoPtr tmp_info = nullptr;
    if (fused_que.empty())
    {
        LogInfo("fused que is empty()\n");
        return tmp_info;
    }

    LogInfo("fused_que size: %ld\n", fused_que.size());

    if (fusion_latest_time_ < 1.0e-3 || fused_que.size() == 1)
    {
        fusion_latest_time_ = fused_que[0].header().local_pose().timestamp();
        tmp_info = std::make_shared<PerceptionObjectInfo>(fused_que[0]);
        LogInfo("first fusion_latest_time_ : %.3lf\n", fusion_latest_time_);
        return tmp_info;
    }

    for (size_t i = 0; i < fused_que.size(); ++i)
    {
        if (fusion_latest_time_ < fused_que[i].header().local_pose().timestamp())
        {
            tmp_info = std::make_shared<PerceptionObjectInfo>(fused_que[i]);
            fusion_latest_time_ = fused_que[i].header().local_pose().timestamp();
            LogInfo("GetLatestFusionInfo, get info, timestamp: %.3lf\n",fusion_latest_time_);
            return tmp_info;
        }
    }
//    for (int i = fused_que.size() - 1; i >= 0; i--)
//    {
//        if (fusion_latest_time_ < fused_que[i].header().local_pose().timestamp() &&
//            fabs(fusion_latest_time_ - fused_que[i].header().local_pose().timestamp()) < 1.0e-2)
//        {
//            tmp_info = std::make_shared<PerceptionObjectInfo>(fused_que[i]);
//            fusion_latest_time_ = fused_que[i].header().local_pose().timestamp();
//            LogDebug(2, "GetLatestFusionInfo, get info, timestamp: %.3lf\n", fusion_latest_time_);
//            return tmp_info;
//        }
//    }

//    int left_idx = 0;
//    int right_idx = fused_que.size() - 1;
//    LogInfo("fusion_latest_time_: %.3lf\n", fusion_latest_time_);

//    int mid_idx = left_idx + (right_idx - left_idx) / 2;
//    while (left_idx <= right_idx)
//    {
//        mid_idx = left_idx + (right_idx - left_idx) / 2;
//        if (fusion_latest_time_ < fused_que[mid_idx].header().local_pose().timestamp() &&
//            fabs(fusion_latest_time_ - fused_que[mid_idx].header().local_pose().timestamp()) < 1.0e-2)
//        {
//            tmp_info = std::make_shared<PerceptionObjectInfo>(fused_que[mid_idx]);
//            fusion_latest_time_ = fused_que[mid_idx].header().local_pose().timestamp();
//            LogDebug(2, "GetLatestFusionInfo, get info, timestamp: %.3lf\n", fusion_latest_time_);
//            return tmp_info;
//        }
//        else if (fusion_latest_time_ < fused_que[mid_idx].header().local_pose().timestamp())
//        {
//            right_idx = mid_idx - 1;
//        }
//        else if (fusion_latest_time_ > fused_que[mid_idx].header().local_pose().timestamp())
//        {
//            left_idx = mid_idx + 1;
//        }
//    }
    LogInfo("GetLatestFusionInfo, not get fused info!\n");
    return tmp_info;
}

LocalHDMapPtr PerceptionDataTrigger::GetHdmapInfo(std::deque<LocalHDMap> hdmap_que, double timestamp)
{
  LocalHDMapPtr tmp_info = nullptr;
  if (hdmap_que.empty()) {
    LogError("hdmap que is empty!\n");
    return tmp_info;
  }

//  for (size_t i = 0; i < hdmap_que.size(); ++i) {
//    LogInfo("hamap, idx: %ld, timestamp: %.3lf\n", i,
//            hdmap_que[i].header().local_pose().timestamp());
//  }

  if(hdmap_que.size() < 2)
  {
      tmp_info = std::make_shared<LocalHDMap>(hdmap_que[0]);
      LogInfo("GetHdmapInfo, get hdmap info, timestamp: %.3lf\n",hdmap_que[0].header().local_pose().timestamp());
      return tmp_info;
  }

  for (size_t i = 0; i < hdmap_que.size(); ++i)
  {
      if(i == hdmap_que.size()-1)
      {
          tmp_info = std::make_shared<LocalHDMap>(hdmap_que[i]);
          LogInfo("GetHdmapInfo, get hdmap info, timestamp: %.3lf\n",hdmap_que[i].header().local_pose().timestamp());
          return tmp_info;
      }

      double difftime = fabs(timestamp - hdmap_que[i].header().local_pose().timestamp());
      if (difftime < fabs(timestamp - hdmap_que[i+1].header().local_pose().timestamp()))
      {
            tmp_info = std::make_shared<LocalHDMap>(hdmap_que[i]);
            LogInfo("GetHdmapInfo, get hdmap info, timestamp: %.3lf\n",hdmap_que[i].header().local_pose().timestamp());
            return tmp_info;
      }
  }

//    int left_idx = 0;
//    int right_idx = hdmap_que.size() - 1;
//    int mid_idx = 0;
//    while (left_idx <= right_idx)
//    {
//        mid_idx = left_idx + (right_idx - left_idx) / 2;
//        if (timestamp >= hdmap_que[mid_idx].header().local_pose().timestamp() && timestamp < hdmap_que[mid_idx+1].header().local_pose().timestamp())
//        {
//            tmp_info = std::make_shared<LocalHDMap>(hdmap_que[mid_idx]);
//            LogDebug(2, "GetHdmapInfo, get hdmap info, timestamp: %.3lf\n",hdmap_que[mid_idx].header().local_pose().timestamp());
//            return tmp_info;
//        }
//        else if (timestamp < hdmap_que[mid_idx].header().local_pose().timestamp())
//        {
//            right_idx = mid_idx - 1;
//        }
//        else if (timestamp > hdmap_que[mid_idx].header().local_pose().timestamp())
//        {
//            left_idx = mid_idx + 1;
//        }
//    }


    LogInfo("GetHdmapInfo, not get hdmap info!\n");
    return tmp_info;
}

// 1) fused objects lost ; 2) single sensor objects lost
bool PerceptionDataTrigger::LossDetectObjects(UnifiedComm* data, PerceptionTriggerParam param)
{
    LogInfo("coming LossDetectObjects \n");
    if(param.collect_flag == false)
    {
        LogInfo("LossDetectObjects collect_flag is false!");
        return false;
    }

    //1、从队列中获取最新一帧PerceptionObjectInfo数据
//    data->m_fused_mutex.lock();
    std::lock_guard<std::mutex> lock_fused(data->m_fused_mutex);
    if(!data->m_fused_queue.empty())
    {
        fused_frame = std::make_shared<PerceptionObjectInfo>(data->m_fused_queue.back());
    }
    else
    {
        LogError("m_fused_queue is empty!\n");
//        data->m_fused_mutex.unlock();
        return false;
    }

    int data_size = data->m_fused_queue.size();
    if(data_size >= 2)
    {
        fused_frame_last = std::make_shared<PerceptionObjectInfo>(data->m_fused_queue[data_size-2]);
    }
    else
    {
        LogError("m_fused_queue data_size is < 2!\n");
        return false;
    }

    fusion_info_ = fused_frame;

    if (fused_frame->obs_objs_size() == 0)
    {
        LogError("m_curr_fused obs_objs_size() = 0\n");
        return false;
    }

    if (fused_frame->obs_objs_size() == 0)
    {
        LogError("m_curr_fused obs_objs_size() = 0\n");
        return false;
    }

    double cur_time = fused_frame->header().local_pose().timestamp();

    std::lock_guard<std::mutex> lock_hdmap(data->m_hdmap_mutex);
    auto hdmap = GetHdmapInfo(data->m_hdmap_queue, cur_time);

    if (nullptr == hdmap)
    {
        LogInfo("lostDetectObjects, hdmap is nullptr--\n");
        return false;
    }

    GetLaneArea(hd_mat_, *hdmap, fused_frame->header().local_pose());

    idx_info_map_.clear();
    for (size_t i = 0; i < fused_frame->obs_objs_size(); ++i)
    {
        const PerceptionObject& obj = fused_frame->obs_objs(i);

        if (IsInROI(obj))
        {
            ObjectInfo obj_info;
            obj_info.object = std::make_shared<PerceptionObject>(obj);
            obj_info.id = obj.track_id();
            obj_info.is_areas = true;
            obj_info.is_init = true;
            obj_info.is_update = true;
            obj_info.latest_timestamp = fused_frame->header().local_pose().timestamp();
            obj_info.duration_timestamp = 0.0;
            idx_info_map_.insert(std::make_pair(obj_info.id, obj_info));
            LogInfo("obj id: %d is in roi and new, latest_timestamp: %.3lf, is_areas: %d\n",
                    obj.track_id(), obj_info.latest_timestamp, obj_info.is_areas);
        }
        else
        {
            ObjectInfo obj_info;
            obj_info.object = std::make_shared<PerceptionObject>(obj);
            obj_info.id = obj.track_id();
            obj_info.is_areas = false;
            obj_info.is_init = true;
            obj_info.is_update = true;
            obj_info.latest_timestamp = fused_frame->header().local_pose().timestamp();
            obj_info.duration_timestamp = 0.0;
            idx_info_map_.insert(std::make_pair(obj_info.id, obj_info));
            LogInfo("obj id: %d is not in roi and new, latest_timestamp: %.3lf, is_areas: %d\n",
                    obj.track_id(), obj_info.latest_timestamp, obj_info.is_areas);
        }
    }

    // check object lost
    printf("curr obj num : %d, last obj num : %d\n",idx_info_map_.size(),fused_frame_last->obs_objs_size());

//    if(idx_info_map_.size() > fused_frame_last->obs_objs_size())
//    {

//    }

    if(idx_info_map_.size() > fused_frame_last->obs_objs_size())
    {
        for (auto it = idx_info_map_.begin(); it != idx_info_map_.end(); ++it)
    {
        if(it->second.is_areas)
        {
            size_t i = 0;
            for (i; i < fused_frame_last->obs_objs_size(); ++i)
            {
                const PerceptionObject& obj = fused_frame_last->obs_objs(i);

                if(obj.track_id() == it->second.id)
                {
                    break;
                }
            }
//            printf("*****i = %d, size = %d\n",i,fused_frame_last->obs_objs_size());
            if(i == fused_frame_last->obs_objs_size())
            {
                printf("目标突现触发采集\n");
                if (!lost_object_flag)
                {
                    data->SendTask(param.task_param);
                    lost_object_flag = true;
                    Timer(param.task_param.collect_time);
                    LogInfo("目标突现触发采集\n");
                    idx_info_map_.clear();
                    break;
                }
            }
        }
    }
    }
    else if(idx_info_map_.size() < fused_frame_last->obs_objs_size())
    {
        for (size_t i = 0; i < fused_frame_last->obs_objs_size(); ++i)
    {
        const PerceptionObject& obj = fused_frame_last->obs_objs(i);
        if (IsInROI(obj))
        {
            auto it = idx_info_map_.begin();
            for (it; it != idx_info_map_.end(); ++it)
            {
                if(obj.track_id() == it->second.id)
                {
                    break;
                }
            }

            if(it == idx_info_map_.end())
            {
                printf("目标丢失触发采集\n");
                if (!lost_object_flag)
                {
                    data->SendTask(param.task_param);
                    lost_object_flag = true;
                    Timer(param.task_param.collect_time);
                    LogInfo("目标丢失触发采集\n");
                    idx_info_map_.clear();
                    break;
                }
            }
        }
    }
    }
//    printf("i = %d, size = %d\n",i,fused_frame_last->obs_objs_size());
//    if(fused_frame_last->obs_objs_size() > i+1)
//    {
//        for(size_t j = i+1; j < fused_frame_last->obs_objs_size(); ++j)
//        {
//            const PerceptionObject& obj = fused_frame_last->obs_objs(j);
//            if (IsInROI(obj))
//            {
//                printf("目标丢失触发采集\n");
//                if (!lost_object_flag)
//                {
//                    data->SendTask(param.task_param);
//                    lost_object_flag = true;
//                    Timer(param.task_param.collect_time);
//                    LogInfo("目标丢失触发采集\n");
//                    idx_info_map_.clear();
//                    break;
//                }
//            }
//        }
//    }





//                    printf("id : %d, curr time : %.3lf, last time : %.3lf\n",it->second.id,it->second.latest_timestamp,fused_frame_last->header().local_pose().timestamp());
//                    if (it->second.latest_timestamp - fused_frame_last->header().local_pose().timestamp() > kTimeThreshold)
//                    {
//                        LogInfo("obj id : %d, cur_time: %.3lf, latest_timestamp: %.3lf, time_diff: %.3lf, more than kTimeThreshold\n",
//                        it->second.id, it->second.latest_timestamp, fused_frame_last->header().local_pose().timestamp(),
//                                it->second.latest_timestamp - fused_frame_last->header().local_pose().timestamp());
//                        // Determine whether the loss is normal or abnormal
//                        if (IsObstructed(it->second.object, &(it->second)))
//                        {
//                            LogInfo("obj id: %d is obstructed, remove\n", it->second.id);
//                            it = idx_info_map_.erase(it);
//                            continue;
//                        }
//                        else
//                        {
//                            LogInfo("data trigger, object id: %d, loss time more 300ms\n",it->second.id);
//                            idx_info_map_.erase(it);
//                            if (!lost_object_flag)
//                            {
//                                data->SendTask(param.task_param);
//                                lost_object_flag = true;
//                                Timer(param.task_param.collect_time);
//                                LogInfo("目标丢失触发采集\n");
//                            }

//                        }
//                    }
//                    if(i == fused_frame_last->obs_objs_size()-1)
//                    {
//                        printf("i : %d, curr id : %d, last id : %d\n",i,it->second.id,obj.track_id());
//                        i++;
//                    }
//                    break;
//                }
//            }
//            if(i == fused_frame_last->obs_objs_size()-1)
//            {
//                if (!lost_object_flag)
//                {
//                    data->SendTask(param.task_param);
//                    lost_object_flag = true;
//                    Timer(param.task_param.collect_time);
//                    LogInfo("目标突现触发采集\n");
//                    idx_info_map_.clear();
//                    break;
//                }
//            }
//        }


//        if (!it->second.is_areas)
//        {
//            LogInfo("obj id: %d is not in areas, remove\n", it->second.id);
//            it = idx_info_map_.erase(it);
//            continue;
//        }

//        if (cur_time - it->second.latest_timestamp > kTimeThreshold)
//        {
//            LogInfo("obj id : %d, cur_time: %.3lf, latest_timestamp: %.3lf, time_diff: %.3lf, more than kTimeThreshold\n",
//            it->second.id, cur_time, it->second.latest_timestamp,cur_time - it->second.latest_timestamp);
//            // Determine whether the loss is normal or abnormal
//            if (IsObstructed(it->second.object, &(it->second)))
//            {
//                LogInfo("obj id: %d is obstructed, remove\n", it->second.id);
//                it = idx_info_map_.erase(it);
//                continue;
//            }
//            else
//            {
//                LogInfo("data trigger, object id: %d, loss time more 300ms\n",it->second.id);
//                idx_info_map_.erase(it);
//                if (!lost_object_flag)
//                {
//                    data->SendTask(param.task_param);
//                    lost_object_flag = true;
//                    Timer(param.task_param.collect_time);
//                    LogInfo("目标丢失触发采集\n");
//                }

//            }
//        }
//    }
//    std::deque<PerceptionObjectInfo> fuse_info_que = data->GetFusedData();
//    if (fuse_info_que.empty())
//    {
//        LogError("fuse_info_que is empty()\n");
//        return false;
//    }

//    auto fused_frame = GetLatestFusionInfo(fuse_info_que);

//    if (nullptr == fused_frame)
//    {
//        LogError("nullptr == fused_frame\n");
//        return false;
//    }

//    fusion_info_ = fused_frame;
/*
    if (fused_frame->obs_objs_size() == 0)
    {
        LogError("fused_frame obs_objs_size() = 0\n");
        return false;
    }

    double cur_time = fused_frame->header().local_pose().timestamp();
    std::deque<LocalHDMap> hdmap_dq = data_read->GetHDmapData();
    auto hdmap = GetHdmapInfo(hdmap_dq, cur_time);

    if (nullptr == hdmap)
    {
        LogInfo("lostDetectObjects, hdmap is nullptr--\n");
        return false;
    }

    GetLaneArea(hd_mat_, *hdmap, fused_frame->header().local_pose());

    for (size_t i = 0; i < fused_frame->obs_objs_size(); ++i)
    {
        const PerceptionObject& obj = fused_frame->obs_objs(i);

        // check obj is in map
        auto it = idx_info_map_.find(obj.track_id());
        if (it == idx_info_map_.end())
        {
            if (IsInROI(obj))
            {
                ObjectInfo obj_info;
                obj_info.object = std::make_shared<PerceptionObject>(obj);
                obj_info.id = obj.track_id();
                obj_info.is_areas = true;
                obj_info.is_init = true;
                obj_info.is_update = true;
                obj_info.latest_timestamp = fused_frame->header().local_pose().timestamp();
                obj_info.duration_timestamp = 0.0;
                idx_info_map_.insert(std::make_pair(obj_info.id, obj_info));
                LogInfo("obj id: %d is in roi and new, latest_timestamp: %.3lf, is_areas: %d\n",
                        obj.track_id(), obj_info.latest_timestamp, obj_info.is_areas);
            }
        }
        else
        {
            if (IsInROI(obj))
            {
                // check object loss time;
//                if (obj.loss_time() > kTimeThreshold)
//                {
//                    LogInfo("obj id: %d,  loss_time(): %.3lf,  more than kTimeThreshold\n", obj.track_id(), obj.loss_time());
//                    if (param.lost_object_flag)
//                    {
//                        if (!lost_object_flag)
//                        {
//                            data_read->SendTask(param.task_param);
//                            lost_object_flag = true;
//                            Timer(param.task_param.collect_time);
//                            LogInfo("Data collect...........\n");
//                        }
//                    }
//                    return true;
//                }
//                else
//                {
                    it->second.is_update = true;
                    it->second.object = std::make_shared<PerceptionObject>(obj);
                    it->second.duration_timestamp = cur_time - it->second.latest_timestamp;
                    it->second.latest_timestamp = cur_time;
                    LogInfo("obj id: %d update, latest_timestamp: %.3lf\n", obj.track_id(), it->second.latest_timestamp);
//                }
            }
            else
            {
                it->second.is_areas = false;
                LogInfo("obj id: %d is not in roi, is_areas: %d\n", obj.track_id(), it->second.is_areas);
            }
        }

    }

    // check object lost
    for (auto it = idx_info_map_.begin(); it != idx_info_map_.end(); ++it)
    {
        if (!it->second.is_areas)
        {
            LogInfo("obj id: %d is not in areas, remove\n", it->second.id);
            it = idx_info_map_.erase(it);
            continue;
        }

        if (cur_time - it->second.latest_timestamp > kTimeThreshold)
        {
            LogInfo("obj id : %d, cur_time: %.3lf, latest_timestamp: %.3lf, time_diff: %.3lf, more than kTimeThreshold\n",
            it->second.id, cur_time, it->second.latest_timestamp,cur_time - it->second.latest_timestamp);
            // Determine whether the loss is normal or abnormal
            if (IsObstructed(it->second.object, &(it->second)))
            {
                LogInfo("obj id: %d is obstructed, remove\n", it->second.id);
                it = idx_info_map_.erase(it);
                continue;
            }
            else
            {
                LogInfo("data trigger, object id: %d, loss time more 300ms\n",it->second.id);
                // idx_info_map_.erase(it);
                if (!lost_object_flag)
                {
                    data_read->SendTask(param.task_param);
                    lost_object_flag = true;
                    Timer(param.task_param.collect_time);
                    LogInfo("目标丢失触发采集\n");
                }

            }

        }
    }
*/
    return false;
}

// bool PerceptionDataTrigger::FalseAlarmObjects() {}

// bool PerceptionDataTrigger::UnstableObjects() {}

bool PerceptionDataTrigger::IsObstructed(const PerceptionObjectPtr& obj,
                                         ObjectInfo* obj_info)
{
  if (nullptr == obj || nullptr == obj_info) {
    LogInfo("IsObstructed, object is nullptr!\n");
    return false;
  }

  if (nullptr == obj_info) {
    LogInfo("IsObstructed, obj_info is nullptr!\n");
    return false;
  }

  // Linear Equation
  if (fabs(obj->center().x() - 0.0) < 1e-2) {
    for (size_t i = 0; i < fusion_info_->obs_objs_size(); ++i) {
      auto tmp_obj = fusion_info_->obs_objs(i);
      double half_diagonal = sqrt(tmp_obj.length() * tmp_obj.length() +
                                  tmp_obj.width() * tmp_obj.width()) /
                             2.0;
      if (tmp_obj.center().x() - half_diagonal < 0) {
        obj_info->is_obstructed = true;
        LogInfo("IsObstructed, obj id: %d is obstructed, is_obstructed: %d\n",
                obj_info->id, obj_info->is_obstructed);
        return true;
      }
    }

  } else {
    double k = obj->center().y() / obj->center().x();
    for (size_t i = 0; i < fusion_info_->obs_objs_size(); ++i) {
      auto tmp_obj = fusion_info_->obs_objs(i);
      double half_diagonal = sqrt(tmp_obj.length() * tmp_obj.length() +
                                  tmp_obj.width() * tmp_obj.width()) /
                             2.0;
      bool is_obstructed =
          fabs(tmp_obj.center().y() - k * tmp_obj.center().x()) /
              sqrt(k * k + 1) -
          half_diagonal;
      if (!is_obstructed) {
        obj_info->is_obstructed = true;
        LogInfo("IsObstructed, obj id: %d is obstructed, is_obstructed: %d\n",
                obj_info->id, obj_info->is_obstructed);
        return true;
      }
    }
  }
  return false;
}

bool PerceptionDataTrigger::IsInROI(const PerceptionObject& object) {
  //cv::Mat hd_map = GetHdMat();
  bool is_in_roi = false;
  if (fabs(object.center().x()) <= kROIxminmax &&
      object.center().y() <= kROIymax && object.center().y() >= kROIymin) {
    is_in_roi = true;
  }
  // Todo conside IsObjectInLaneArea(hd_map, object)
  return is_in_roi;
}

bool PerceptionDataTrigger::IsObjectInLaneArea(const cv::Mat& lane_map,
                                               const PerceptionObject& obj) {
  MapRange map_range;
  cv::Point tmp_pt_0;
  auto center = obj.center();
  tmp_pt_0.x =
      static_cast<int>((center.x() + map_range.x_max) / map_range.resolution);
  tmp_pt_0.y =
      static_cast<int>((map_range.y_max - center.y()) / map_range.resolution);
  if (tmp_pt_0.x < 0 ||
      tmp_pt_0.x >=
          int((map_range.x_max - map_range.x_min) / map_range.resolution) ||
      tmp_pt_0.y < 0 ||
      tmp_pt_0.y >=
          int((map_range.y_max - map_range.y_min) / map_range.resolution)) {
    return false;
  }

  if (lane_map.at<uchar>(tmp_pt_0.y, tmp_pt_0.x) > 0) {
    return true;
  }

  // Determine if corner in lane areas
  cv::Point tmp_corner;
  for (size_t i = 0; i < obj.corner().x_size(); ++i) {
    tmp_corner.x = static_cast<int>((obj.corner().x(i) + map_range.x_max) /
                                    map_range.resolution);
    tmp_corner.y = static_cast<int>((map_range.y_max - obj.corner().y(i)) /
                                    map_range.resolution);
    if (tmp_corner.x < 0 ||
        tmp_corner.x >=
            int((map_range.x_max - map_range.x_min) / map_range.resolution) ||
        tmp_corner.y < 0 ||
        tmp_corner.y >=
            int((map_range.y_max - map_range.y_min) / map_range.resolution)) {
      return false;
    }

    if (lane_map.at<uchar>(tmp_corner.y, tmp_corner.x) > 0) {
      return true;
    }
  }

  LogInfo("obj id: %d, is not in land areas\n", obj.track_id());

  return false;
}

void PerceptionDataTrigger::GetLaneArea(cv::Mat& lane_map,
                                        const LocalHDMap& local_hdmap,
                                        const LocalPose& curr_lp) {
  // 1. 定义目标填充图
  MapRange map_range;
  int map_size_x = int(
      std::round((map_range.x_max - map_range.x_min) / map_range.resolution));
  int map_size_y = int(
      std::round((map_range.y_max - map_range.y_min) / map_range.resolution));
  lane_map.release();
  lane_map = cv::Mat::zeros(map_size_y, map_size_x, CV_8U);
  // 2. 计算变换矩阵
  cv::Mat v2l = cv::Mat::eye(3, 3, CV_32F);
  float yaw = (curr_lp.dr_heading() - 90.0) / 180.0 * M_PI;

  v2l.at<float>(0, 0) = std::cos(yaw);
  v2l.at<float>(1, 1) = std::cos(yaw);
  v2l.at<float>(0, 1) = -std::sin(yaw);
  v2l.at<float>(1, 0) = std::sin(yaw);
  v2l.at<float>(0, 2) = curr_lp.dr_x();
  v2l.at<float>(1, 2) = curr_lp.dr_y();
  cv::Mat l2v;
  cv::invert(v2l, l2v);
  // 3. 分别提取前向车道线和后向车道线
  std::vector<MapLaneMarking> f_proto_lmk;
  std::vector<MapLaneMarking> r_proto_lmk;
  std::vector<MapPassableArea> proto_map;
  if (local_hdmap.has_header()) {
    if (local_hdmap.future_lanemarkings_size() > 1) {
      for (int i = 0; i < local_hdmap.future_lanemarkings_size(); ++i) {
        if (local_hdmap.future_lanemarkings(i).ptx_size() > 0) {
          bool valid_flag = true;
          for (int j = 1; j < local_hdmap.future_lanemarkings(i).ptx_size();
               ++j) {
            double dis_x = local_hdmap.future_lanemarkings(i).ptx(j) -
                           local_hdmap.future_lanemarkings(i).ptx(j - 1);
            double dis_y = local_hdmap.future_lanemarkings(i).pty(j) -
                           local_hdmap.future_lanemarkings(i).pty(j - 1);
            if (dis_x * dis_x + dis_y * dis_y > 100) {
              valid_flag = false;
              break;
            }
          }
          if (valid_flag) {
            f_proto_lmk.push_back(local_hdmap.future_lanemarkings(i));
          }
        }
      }
    }
    if (local_hdmap.past_lanemarkings_size() > 1) {
      for (int i = 0; i < local_hdmap.past_lanemarkings_size(); ++i) {
        if (local_hdmap.past_lanemarkings(i).ptx_size() > 0) {
          bool valid_flag = true;
          for (int j = 1; j < local_hdmap.past_lanemarkings(i).ptx_size();
               ++j) {
            double dis_x = local_hdmap.past_lanemarkings(i).ptx(j) -
                           local_hdmap.past_lanemarkings(i).ptx(j - 1);
            double dis_y = local_hdmap.past_lanemarkings(i).pty(j) -
                           local_hdmap.past_lanemarkings(i).pty(j - 1);
            if (dis_x * dis_x + dis_y * dis_y > 100) {
              valid_flag = false;
              break;
            }
          }
          if (valid_flag) {
            r_proto_lmk.push_back(local_hdmap.past_lanemarkings(i));
          }
        }
      }
    }
    if (local_hdmap.passable_areas_size() > 0) {
      for (int i = 0; i < local_hdmap.passable_areas_size(); ++i) {
        if (local_hdmap.passable_areas(i).ptx_size() > 0) {
          proto_map.push_back(local_hdmap.passable_areas(i));
        }
      }
    }
  }

  // 4. 取车道线的外包络边界
  if (f_proto_lmk.size() > 1 || r_proto_lmk.size() > 1) {
    // 根据不同情况进行车道线区域拼接，顺时针方向
    int f_lmk_num = f_proto_lmk.size();
    int r_lmk_num = r_proto_lmk.size();
    if (f_lmk_num > 1) {
      for (int curr_i = 1; curr_i < f_lmk_num; ++curr_i) {
        int last_i = curr_i - 1;
        int curr_num = f_proto_lmk[curr_i].ptx_size();
        int last_num = f_proto_lmk[last_i].ptx_size();
        if (curr_num > 1 && last_num > 1) {
          std::vector<cv::Point> interest_area;
          for (int j = 0; j < last_num; ++j) {
            float x = f_proto_lmk[last_i].ptx(j) * l2v.at<float>(0, 0) +
                      f_proto_lmk[last_i].pty(j) * l2v.at<float>(0, 1) +
                      l2v.at<float>(0, 2);
            float y = f_proto_lmk[last_i].ptx(j) * l2v.at<float>(1, 0) +
                      f_proto_lmk[last_i].pty(j) * l2v.at<float>(1, 1) +
                      l2v.at<float>(1, 2);

            int x_idx =
                int(std::round((x + map_range.x_max) / map_range.resolution));
            int y_idx =
                int(std::round((map_range.y_max - y) / map_range.resolution));

            if (x_idx <= 0 ||
                x_idx >= int((map_range.x_max - map_range.x_min) /
                             map_range.resolution) ||
                y_idx <= 0 ||
                y_idx >= int((map_range.y_max - map_range.y_min) /
                             map_range.resolution)) {
              continue;
            }
            interest_area.push_back(cv::Point(x_idx, y_idx));
          }

          for (int j = curr_num - 1; j > -1; --j) {
            float x = f_proto_lmk[curr_i].ptx(j) * l2v.at<float>(0, 0) +
                      f_proto_lmk[curr_i].pty(j) * l2v.at<float>(0, 1) +
                      l2v.at<float>(0, 2);
            float y = f_proto_lmk[curr_i].ptx(j) * l2v.at<float>(1, 0) +
                      f_proto_lmk[curr_i].pty(j) * l2v.at<float>(1, 1) +
                      l2v.at<float>(1, 2);
            int x_idx =
                int(std::round((x + map_range.x_max) / map_range.resolution));
            int y_idx =
                int(std::round((map_range.y_max - y) / map_range.resolution));

            if (x_idx <= 0 ||
                x_idx >= int((map_range.x_max - map_range.x_min) /
                             map_range.resolution) ||
                y_idx <= 0 ||
                y_idx >= int((map_range.y_max - map_range.y_min) /
                             map_range.resolution)) {
              continue;
            }

            interest_area.push_back(cv::Point(x_idx, y_idx));
          }

          if (interest_area.size() < 3) {
            continue;
          }
          std::vector<std::vector<cv::Point> > temp_contour;
          temp_contour.push_back(interest_area);
          cv::fillPoly(lane_map, temp_contour, cv::Scalar(255));
        }
      }
    }
    if (r_lmk_num > 1) {
      for (int curr_i = 1; curr_i < r_lmk_num; ++curr_i) {
        int last_i = curr_i - 1;
        int curr_num = r_proto_lmk[curr_i].ptx_size();
        int last_num = r_proto_lmk[last_i].ptx_size();
        if (curr_num > 1 && last_num > 1) {
          std::vector<cv::Point> interest_area;
          for (int j = 0; j < last_num; ++j) {
            float x = r_proto_lmk[last_i].ptx(j) * l2v.at<float>(0, 0) +
                      r_proto_lmk[last_i].pty(j) * l2v.at<float>(0, 1) +
                      l2v.at<float>(0, 2);
            float y = r_proto_lmk[last_i].ptx(j) * l2v.at<float>(1, 0) +
                      r_proto_lmk[last_i].pty(j) * l2v.at<float>(1, 1) +
                      l2v.at<float>(1, 2);
            int x_idx =
                int(std::round((x + map_range.x_max) / map_range.resolution));
            int y_idx =
                int(std::round((map_range.y_max - y) / map_range.resolution));
            if (x_idx <= 0 ||
                x_idx >= int((map_range.x_max - map_range.x_min) /
                             map_range.resolution) ||
                y_idx <= 0 ||
                y_idx >= int((map_range.y_max - map_range.y_min) /
                             map_range.resolution)) {
              continue;
            }
            interest_area.push_back(cv::Point(x_idx, y_idx));
          }

          for (int j = curr_num - 1; j > -1; --j) {
            float x = r_proto_lmk[curr_i].ptx(j) * l2v.at<float>(0, 0) +
                      r_proto_lmk[curr_i].pty(j) * l2v.at<float>(0, 1) +
                      l2v.at<float>(0, 2);
            float y = r_proto_lmk[curr_i].ptx(j) * l2v.at<float>(1, 0) +
                      r_proto_lmk[curr_i].pty(j) * l2v.at<float>(1, 1) +
                      l2v.at<float>(1, 2);
            int x_idx =
                int(std::round((x + map_range.x_max) / map_range.resolution));
            int y_idx =
                int(std::round((map_range.y_max - y) / map_range.resolution));
            if (x_idx < 0 ||
                x_idx >= int((map_range.x_max - map_range.x_min) /
                             map_range.resolution) ||
                y_idx < 0 ||
                y_idx >= int((map_range.y_max - map_range.y_min) /
                             map_range.resolution)) {
              continue;
            }
            interest_area.push_back(cv::Point(x_idx, y_idx));
          }

          if (interest_area.size() < 3) {
            continue;
          }
          std::vector<std::vector<cv::Point> > temp_contour;
          temp_contour.push_back(interest_area);
          cv::fillPoly(lane_map, temp_contour, cv::Scalar(255));
        }
      }
    }
  } else {
    // 可通行区域
    for (uint i = 0; i < proto_map.size(); ++i) {
      std::vector<cv::Point> interest_area;
      for (int j = 0; j < proto_map[i].ptx_size(); ++j) {
        float x = proto_map[i].ptx(j) * l2v.at<float>(0, 0) +
                  proto_map[i].pty(j) * l2v.at<float>(0, 1) +
                  l2v.at<float>(0, 2);
        float y = proto_map[i].ptx(j) * l2v.at<float>(1, 0) +
                  proto_map[i].pty(j) * l2v.at<float>(1, 1) +
                  l2v.at<float>(1, 2);
        int x_idx =
            int(std::round((x + map_range.x_max) / map_range.resolution));
        int y_idx =
            int(std::round((map_range.y_max - y) / map_range.resolution));

        if (x_idx <= 0 ||
            x_idx >= int((map_range.x_max - map_range.x_min) /
                         map_range.resolution) ||
            y_idx <= 0 ||
            y_idx >= int((map_range.y_max - map_range.y_min) /
                         map_range.resolution)) {
          continue;
        }
        interest_area.push_back(cv::Point(x_idx, y_idx));
      }
      if (interest_area.size() < 3) {
        continue;
      }
      std::vector<std::vector<cv::Point> > temp_contour;
      temp_contour.push_back(interest_area);
      cv::fillPoly(lane_map, temp_contour, cv::Scalar(255));
    }
  }
}

void PerceptionDataTrigger::Timer(int time_sec) {
  LogInfo("Timer...\n");
  std::thread([this, time_sec]() {
    std::this_thread::sleep_for(std::chrono::seconds(time_sec));
    lost_object_flag = false;
  }).detach();
}
