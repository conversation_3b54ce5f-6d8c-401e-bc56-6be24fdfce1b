#include "trafficlight_trigger.h"

TrafficLightTrigger::TrafficLightTrigger()
{
//    std::cout << "TrafficTrigger" << std::endl;
}

TrafficLightTrigger::~TrafficLightTrigger()
{
//    std::cout << "~TrafficTrigger" << std::endl;
}

//根据当前时刻lpt二分查找最接近的数据
int TrafficLightTrigger::SearchData(double Val, std::deque<LocalHdmapData> data)
{
    int low = 0;
    int high = data.size() - 1;

    while (low <= high)
    {
        int m = low + (high - low) / 2;

        if ((Val>data[m].header().timestamp()-0.5)&&(Val<data[m].header().timestamp()+0.5))
        {
            return m;
        }
        else if (Val < data[m].header().timestamp())
        {
            high = m - 1;
        }
        else
        {
            low = m + 1;
        }
    }
    return -1;
}

int TrafficLightTrigger::SearchData(double Val, std::deque<TrafficLightData> data)
{
    int low = 0;
    int high = data.size() - 1;

    while (low <= high)
    {
        int m = low + (high - low) / 2;

        if ((Val>data[m].header().timestamp()-0.5)&&(Val<data[m].header().timestamp()+0.5))
        {
            return m;
        }
        else if (Val < data[m].header().timestamp())
        {
            high = m - 1;
        }
        else
        {
            low = m + 1;
        }
    }
    return -1;
}

// 计算两点间向量与x轴夹角的函数

// 返回的是弧度值，如果需要角度值，可以将结果乘以(180.0/M_PI)

double TrafficLightTrigger::calculateAngle(double x1, double y1, double x2, double y2)
{
    // 计算向量 (x2-x1, y2-y1)
    double dx = x2 - x1;
    double dy = y2 - y1;
    // 使用atan2计算向量与x轴正方向之间的夹角
    // atan2的参数顺序是 (y, x)，即dy, dx
    double angle = atan2(dy, dx);
    // 如果需要角度值而不是弧度值
    double angleDegrees = angle * (180.0 / M_PI);
    return angleDegrees;
}

void TrafficLightTrigger::timer_handler(int time_sec, bool flag)
{
    std::thread([this, time_sec]()
    {
        std::this_thread::sleep_for(std::chrono::seconds(time_sec));
        m_stopline_distance_flag = false;
    }).detach();
}

bool TrafficLightTrigger::StopLineDistanceLogic(UnifiedComm *data, TrafficCollectParam param)
{
    LogInfo("coming StopLineDistanceLogic \n");
    if(param.collect_flag == false)
    {
        LogInfo("StopLineDistanceLogic collect_flag is false!");
        return false;
    }
//    printf("StopLineDistanceLogic\n");
    //1、从队列中获取最新一帧hdmap数据
    data->m_hdmap_mutex.lock();
    if(!data->m_hdmap_queue.empty())
    {
        m_curr_hdmap = data->m_hdmap_queue.back();
    }
    else
    {
        LogInfo("m_hdmap_queue is empty!\n");
//        printf("m_hdmap_queue is empty!\n");
        data->m_hdmap_mutex.unlock();
        return false;
    }
    data->m_hdmap_mutex.unlock();
    LocalPoseData curr_lp = m_curr_hdmap.header().local_pose();
//    printf("curr_lp=%lf\n",curr_lp.timestamp());
    //2、计算变换矩阵
    cv::Mat v2l = cv::Mat::eye(3, 3, CV_32F);
    float yaw = (curr_lp.dr_heading() - 90.0 ) / 180.0 * M_PI;
    v2l.at<float>(0, 0) = std::cos(yaw);
    v2l.at<float>(1, 1) = std::cos(yaw);
    v2l.at<float>(0, 1) = -std::sin(yaw);
    v2l.at<float>(1, 0) = std::sin(yaw);
    v2l.at<float>(0, 2) = curr_lp.dr_x();
    v2l.at<float>(1, 2) = curr_lp.dr_y();
    cv::Mat l2v;
    cv::invert(v2l, l2v);
    //3、检索对应时刻红绿灯信息
//    int index = SearchData(curr_lp.timestamp(), data->m_trafficlight_queue);
//    if(index < 0)
//    {
//        return false;
//    }
//    m_curr_trafficlight = data->m_trafficlight_queue[index];
    //4、找到唯一一条正前方停止线
//    printf("Logicstop_lines_size=%d\n",m_curr_hdmap.stop_lines_size());
    std::map<double,int> distances_id;
    double points[2][2] = {0};
    cv::Vec3d transformedVec,transformedVec1,transformedVec2;
    for(int i = 0; i < m_curr_hdmap.stop_lines_size(); i++)
    {
//        printf("Logicstop_line_type=%d\n",m_curr_hdmap.stop_lines(i).line_type());
        if(m_curr_hdmap.stop_lines(i).line_type() == 2)//筛选红绿灯停止线
        {
//            printf("Logicstop_line_ptx_size=%d\n",m_curr_hdmap.stop_lines(i).ptx_size());
            points[0][0] = m_curr_hdmap.stop_lines(i).ptx(0);
            points[0][1] = m_curr_hdmap.stop_lines(i).pty(0);
            points[1][0] = m_curr_hdmap.stop_lines(i).ptx(1);
            points[1][1] = m_curr_hdmap.stop_lines(i).pty(1);
            cv::Vec3d vec1(points[0][0], points[0][1], 1);
            cv::Vec3d vec2(points[1][0], points[1][1], 1);
            transformedVec1[0] = vec1[0] * l2v.at<float>(0, 0) + vec1[1] * l2v.at<float>(0, 1) + vec1[2] * l2v.at<float>(0, 2);
            transformedVec1[1] = vec1[0] * l2v.at<float>(1, 0) + vec1[1] * l2v.at<float>(1, 1) + vec1[2] * l2v.at<float>(1, 2);
            transformedVec2[0] = vec2[0] * l2v.at<float>(0, 0) + vec2[1] * l2v.at<float>(0, 1) + vec2[2] * l2v.at<float>(0, 2);
            transformedVec2[1] = vec2[0] * l2v.at<float>(1, 0) + vec2[1] * l2v.at<float>(1, 1) + vec2[2] * l2v.at<float>(1, 2);
            if(transformedVec1[1] > 0 && transformedVec2[1] > 0)//筛选车辆前方的停止线
            {
                double angle_x = calculateAngle(transformedVec1[0],transformedVec1[1],transformedVec2[0],transformedVec2[1]);
//                printf("angle_x=%lf\n",angle_x);
                if(135 < angle_x || angle_x < -135 || -45 < angle_x || angle_x < 45)//筛选平行x轴的停止线
                {
                    double sumX = 0, sumY = 0;
                    for(int j = 0; j < m_curr_hdmap.stop_lines(i).ptx_size(); j++)
                    {
                        sumX += m_curr_hdmap.stop_lines(i).ptx(j);
                        sumY += m_curr_hdmap.stop_lines(i).pty(j);
                    }
                    double midX = sumX / m_curr_hdmap.stop_lines(i).ptx_size();
                    double midY = sumY / m_curr_hdmap.stop_lines(i).ptx_size();
                    cv::Vec3d vec(midX, midY, 1);
                    transformedVec[0] = vec[0] * l2v.at<float>(0, 0) + vec[1] * l2v.at<float>(0, 1) + vec[2] * l2v.at<float>(0, 2);
                    transformedVec[1] = vec[0] * l2v.at<float>(1, 0) + vec[1] * l2v.at<float>(1, 1) + vec[2] * l2v.at<float>(1, 2);
                    double angleRadians_y = atan2(transformedVec[1], transformedVec[0]);
                    double angleDegrees_y = angleRadians_y * (180.0 / M_PI);
//                    printf("angle_y=%lf\n",angleDegrees_y);
                    if(45 < angleDegrees_y && angleDegrees_y < 135)//筛选与y轴夹角45-135的停止线
                    {
                        double distance = sqrt(transformedVec[0] * transformedVec[0] + transformedVec[1] * transformedVec[1]);
                        distances_id.insert(std::make_pair(distance,m_curr_hdmap.stop_lines(i).id()));
                    }
                }
            }
        }
    }
    //5、判断停止线距离
    if(!distances_id.empty())
    {
        for (const auto& pair : distances_id)
        {
            LogInfo("distance=%0.3lf\n",pair.first);
//            printf("distance=%0.3lf\n",pair.first);
            if(pair.first <= param.stopline_distance)
            {
                if(!m_stopline_distance_flag)
                {
                    if(m_last_stopline_id != pair.second)
                    {
                        m_last_stopline_id = pair.second;
                        data->SendTask(param.task_param);
                        m_stopline_distance_flag = true;
                        timer_handler(param.task_param.collect_time,m_stopline_distance_flag);
                        LogInfo("停止线距离触发采集\n");
//                        printf("停止线距离触发采集\n");
                        break;
                    }
                }
            }
        }
    }
//                if(m_curr_trafficlight.traffic_light().forward_type() == 0 && m_curr_trafficlight.traffic_light().left_type() == 0
//                        && m_curr_trafficlight.traffic_light().right_type() == 0 && m_curr_trafficlight.traffic_light().uturn_type() == 0)
//                {
//                    printf("触发采集！\n");
//                }

    return true;
}

bool TrafficLightTrigger::TrafficChangeLogic(UnifiedComm *data, TrafficCollectParam param)
{
    LogInfo("coming TrafficChangeLogic \n");
    if(param.collect_flag == false)
    {
        LogInfo("TrafficChangeLogic collect_flag is false!");
        return false;
    }
    //1、从队列中获取最新一帧Traffic数据
    data->m_trafficlight_mutex.lock();
    if(!data->m_trafficlight_queue.empty())
    {
        m_curr_trafficlight = data->m_trafficlight_queue.back();
    }
    else
    {
        LogError("m_trafficlight_queue is empty!\n");
//        printf("m_trafficlight_queue is empty!\n");
        data->m_trafficlight_mutex.unlock();
        return false;
    }

    //2、判断连续n帧红绿灯状态没有跳变
    int data_size = data->m_trafficlight_queue.size();
//    printf("data_size=%d\n",data->m_trafficlight_queue.size());
    if(data_size >= param.change_time/100 + 2)
    {
        if(m_curr_trafficlight.traffic_light().forward_type() != 0)
        {
            if(data->m_trafficlight_queue[data_size-2].traffic_light().forward_type() != m_curr_trafficlight.traffic_light().forward_type())
            {
                int i;
                for(i = 0; i < (param.change_time/100-1); i++)
                {
                    if(data->m_trafficlight_queue[data_size-(3+i)].traffic_light().forward_type() != data->m_trafficlight_queue[data_size-2].traffic_light().forward_type())
                    {
                        LogInfo("forward_type = %d\n",m_curr_trafficlight.traffic_light().forward_type());
                        LogInfo("forward_type = %d\n",data->m_trafficlight_queue[data_size-2].traffic_light().forward_type());
                        LogInfo("forward_type = %d\n",data->m_trafficlight_queue[data_size-(3+i)].traffic_light().forward_type());
                        LogInfo("i = %d\n",i);
                        break;
                    }
                }
                if(i != (param.change_time/100-1))
                {
                    data->SendTask(param.task_param);
                    m_trafficlight_change_flag = true;
                    timer_handler(param.task_param.collect_time,m_trafficlight_change_flag);
                    LogInfo("直行灯跳变触发采集\n");
                }
            }
        }
        if(m_curr_trafficlight.traffic_light().left_type() != 0)
        {
            if(data->m_trafficlight_queue[data_size-2].traffic_light().left_type() != m_curr_trafficlight.traffic_light().left_type())
            {
                int i;
                for(i = 0; i < (param.change_time/100-1); i++)
                {
                    if(data->m_trafficlight_queue[data_size-(3+i)].traffic_light().left_type() != data->m_trafficlight_queue[data_size-2].traffic_light().left_type())
                    {
                        LogInfo("left_type = %d\n",m_curr_trafficlight.traffic_light().left_type());
                        LogInfo("left_type = %d\n",data->m_trafficlight_queue[data_size-2].traffic_light().left_type());
                        LogInfo("left_type = %d\n",data->m_trafficlight_queue[data_size-(3+i)].traffic_light().left_type());
                        LogInfo("i = %d\n",i);
                        break;
                    }
                }
                if(i != (param.change_time/100-1))
                {
                    data->SendTask(param.task_param);
                    m_trafficlight_change_flag = true;
                    timer_handler(param.task_param.collect_time,m_trafficlight_change_flag);
                    LogInfo("左转灯跳变触发采集\n");
                }
            }
        }
        if(m_curr_trafficlight.traffic_light().right_type() != 0)
        {
            if(data->m_trafficlight_queue[data_size-2].traffic_light().right_type() != m_curr_trafficlight.traffic_light().right_type())
            {
                int i;
                for(i = 0; i < (param.change_time/100-1); i++)
                {
                    if(data->m_trafficlight_queue[data_size-(3+i)].traffic_light().right_type() != data->m_trafficlight_queue[data_size-2].traffic_light().right_type())
                    {
                        LogInfo("right_type = %d\n",m_curr_trafficlight.traffic_light().right_type());
                        LogInfo("right_type = %d\n",data->m_trafficlight_queue[data_size-2].traffic_light().right_type());
                        LogInfo("right_type = %d\n",data->m_trafficlight_queue[data_size-(3+i)].traffic_light().right_type());
                        LogInfo("i = %d\n",i);
                        break;
                    }
                }
                if(i != (param.change_time/100-1))
                {
                    data->SendTask(param.task_param);
                    m_trafficlight_change_flag = true;
                    timer_handler(param.task_param.collect_time,m_trafficlight_change_flag);
                    LogInfo("右转灯跳变触发采集\n");
                }
            }
        }
        if(m_curr_trafficlight.traffic_light().uturn_type() != 0)
        {
            if(data->m_trafficlight_queue[data_size-2].traffic_light().uturn_type() != m_curr_trafficlight.traffic_light().uturn_type())
            {
                int i;
                for(i = 0; i < (param.change_time/100-1); i++)
                {
                    if(data->m_trafficlight_queue[data_size-(3+i)].traffic_light().uturn_type() != data->m_trafficlight_queue[data_size-2].traffic_light().uturn_type())
                    {
                        LogInfo("uturn_type = %d\n",m_curr_trafficlight.traffic_light().uturn_type());
                        LogInfo("uturn_type = %d\n",data->m_trafficlight_queue[data_size-2].traffic_light().uturn_type());
                        LogInfo("uturn_type = %d\n",data->m_trafficlight_queue[data_size-(3+i)].traffic_light().uturn_type());
                        LogInfo("i = %d\n",i);
                        break;
                    }
                }
                if(i != (param.change_time/100-1))
                {
                    data->SendTask(param.task_param);
                    m_trafficlight_change_flag = true;
                    timer_handler(param.task_param.collect_time,m_trafficlight_change_flag);
                    LogInfo("掉头灯跳变触发采集\n");
                }
            }
        }
    }
    else
    {
        LogError("m_trafficlight_queue size = %d\n",data_size);
    }
    //3、根据周围车流情况判断红绿灯状态是否错误
    //3.1 直行灯
//    if(m_curr_trafficlight.traffic_light().forward_type() == 1)
//    {
//        printf("触发采集！\n");
//    }
    data->m_trafficlight_mutex.unlock();
    return true;
}

