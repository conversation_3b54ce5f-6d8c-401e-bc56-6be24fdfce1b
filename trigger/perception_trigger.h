#ifndef PERCEPTION_DATA_TRIGGER_H_
#define PERCEPTION_DATA_TRIGGER_H_

#include <deque>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <unordered_map>

#include "unified_comm.h"
#include "hdmap/local_hdmap.pb.h"
#include "perception/perception_common.pb.h"
#include "perception/perception_object_info.pb.h"

using xsproto::base::LocalPose;
using xsproto::hdmap::LocalHDMap;
using xsproto::hdmap::MapLaneMarking;
using xsproto::hdmap::MapPassableArea;
using xsproto::perception::PerceptionObject;
using xsproto::perception::PerceptionObjectInfo;

typedef std::shared_ptr<PerceptionObjectInfo> PerceptionObjectInfoPtr;
typedef std::shared_ptr<PerceptionObject> PerceptionObjectPtr;
typedef std::shared_ptr<LocalHDMap> LocalHDMapPtr;

struct ObjectInfo {
    uint32_t id = 0;
    PerceptionObjectPtr object = nullptr;
    bool is_init = false; // first in areas
    bool is_areas = false;
    bool is_obstructed = false;
    bool is_update = false;
    double timestamp = 0.0; // object timestamp;
    double latest_timestamp = 0.0;
    double duration_timestamp = 0.0; // duration timestamp in areas
};

struct MapRange {
    float x_max = 60;
    float x_min = -60;
    float y_max = 80;
    float y_min = -80;
    float resolution = 0.1;
};



class PerceptionDataTrigger {
public:
    PerceptionDataTrigger() = default;

    //    PerceptionDataTrigger(Communicator* communicator);
    ~PerceptionDataTrigger() = default;

    // Timer
    void Timer(int time_sec);

    // Get data
    PerceptionObjectInfoPtr GetLatestFusionInfo(std::deque<PerceptionObjectInfo> fused_que);

    PerceptionObjectInfoPtr GetLidarInfo(double timestamp);

    PerceptionObjectInfoPtr GetCameraInfo(double timestamp);

    PerceptionObjectInfoPtr GetRadarInfo(double timestamp);

    LocalHDMapPtr GetHdmapInfo(std::deque<LocalHDMap> hdmap_que, double timestamp);

    inline cv::Mat GetHdMat() { return hd_mat_; };

    bool LossDetectObjects(UnifiedComm *data, PerceptionTriggerParam param);

    bool FalseAlarmObjects();

    bool UnstableObjects();

private:
    //  Timer flag
    bool lost_object_flag = false;
    cv::Mat hd_mat_;
    double fusion_latest_time_ = 0.0;
    UnifiedComm *communicate_;
    PerceptionObjectInfoPtr fusion_info_;
    LocalHDMapPtr hdmap_info_;
    std::unordered_map<uint32_t, ObjectInfo> idx_info_map_;
    std::unordered_map<uint32_t, ObjectInfo> cache_info_map_;
    LocalHdmapData m_curr_hdmap;
    PerceptionObjectInfoPtr fused_frame;
    PerceptionObjectInfoPtr fused_frame_last;

    bool IsObstructed(const PerceptionObjectPtr &obj, ObjectInfo *obj_info);

    bool IsInROI(const PerceptionObject &object);

    bool IsObjectInLaneArea(const cv::Mat &lane_map, const PerceptionObject &obj);

    void GetLaneArea(cv::Mat &lane_map, const LocalHDMap &local_hdmap, const LocalPose &curr_lp);
};

#endif
