#ifndef UTILS_VIZ_COLOR_HPP
#define UTILS_VIZ_COLOR_HPP

#include <opencv2/core/types.hpp>
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

namespace vz {
    // 通用颜色定义
    const cv::Scalar black_color = cv::Scalar(0, 0, 0);
    const cv::Scalar white_color = cv::Scalar(255, 255, 255);
    const cv::Scalar gray_color = cv::Scalar(200, 200, 200);
    const cv::Scalar dark_gray_color = cv::Scalar(150, 150, 150);
    const cv::Scalar blue_color = cv::Scalar(255, 0, 0);

    const cv::Scalar green_color = cv::Scalar(0, 255, 0);
    const cv::Scalar light_green_color = cv::Scalar(0, 230, 0);
    const cv::Scalar red_color = cv::Scalar(0, 0, 255);
    const cv::Scalar cyan_color = cv::Scalar(255, 255, 0);
    const cv::Scalar llight_green_color = CV_RGB(204, 255, 204);

    const cv::Scalar yellow_color = CV_RGB(255, 255, 0);
    const cv::Scalar light_yellow_color = cv::Scalar(0, 230, 230);
    const cv::Scalar dark_yellow = CV_RGB(255, 191, 0); // yellow

    const cv::Scalar magenta_color = cv::Scalar(255, 0, 255); // 品红色
    const cv::Scalar med_light_yellow_color = CV_RGB(255, 178, 100);

    const cv::Scalar raspberry_color = CV_RGB(227, 11, 92);

    // 网格颜色
    const cv::Scalar grid_color = cv::Scalar(100, 100, 100); // 灰色
    const cv::Scalar arrow_color = cv::Scalar(0, 255, 0); // 绿色

    // 红绿灯颜色
    const cv::Scalar red_light_color = cv::Scalar(0, 0, 255);
    const cv::Scalar green_light_color = cv::Scalar(0, 255, 0);
    const cv::Scalar yellow_light_color = cv::Scalar(0, 255, 255);
    const cv::Scalar unknown_light_color = cv::Scalar(128, 128, 128);
    const cv::Scalar default_light_color = cv::Scalar(255, 255, 255);

    // 道路方向颜色
    const cv::Scalar lane_heading_color = cyan_color;
    // 参考路径颜色
    const cv::Scalar ref_path_color = CV_RGB(255, 0, 0);

    // 轨迹历史颜色
    const cv::Scalar traj_history_color = CV_RGB(255, 255, 0); // yellow

    // agent颜色
    const cv::Scalar agent_current_color = CV_RGB(255, 255, 0); // yellow
    //const cv::Scalar agent_past_color = CV_RGB(200, 200, 200);
    const cv::Scalar agent_box_color = CV_RGB(255, 165, 0); // orange
    const cv::Scalar agent_heading_color = raspberry_color;
    const cv::Scalar agent_text_color = CV_RGB(0, 255, 0); // green

    // agent prediction color
    const cv::Scalar agent_prediction_color = CV_RGB(255, 215, 0); // gold

    const cv::Scalar plan_candidate = CV_RGB(255, 255, 255); // white
    const cv::Scalar plan_best = CV_RGB(0, 255, 0);
    const cv::Scalar ref_free = agent_prediction_color;

    const struct WindowTitle {
        std::string input_proto = "planner_proto";
        std::string input_data = "planner_input";
        std::string planner = "planner_output";
    }win_title;

    // 使用静态函数返回静态局部变量，确保全局只有一个实例
    // static WindowTitle& GetWindowTitle() {
    //     static WindowTitle win_title;
    //     return win_title;
    // }
    
    // 为了保持兼容性，提供一个引用
    //static WindowTitle& win_title = GetWindowTitle();

    // 定义地图元素样式结构体，用于统一绘制和图例
    struct MapElementStyle {
        const char *name; // 元素名称

        cv::Scalar line_color; // 线条颜色
        int line_width; // 线宽

        cv::Scalar point_color; // 点颜色
        int point_size; // 点大小

        bool is_polygon; // 是否为多边形
    };
    // 用于索引地图样式的枚举
    enum MapElementType {
        LANE_BOUNDARY = 0,
        LANE_CENTER = 1,
        ROAD_BOUNDARY = 2,
        INTERSECTION_OBJ = 3,
        CROSSWALK = 4,
        STOP_REGION = 5,
        ENTRY_REGION = 6,
        TRAFFIC_LIGHT = 7,
        TRAFFIC_SIGNX = 8,
        TASK_POINTS = 9,
        ROUTE_ELEM = 10,
        INTERSECTION_ELEM = 11,
        EGO_CAR = 12,
        MERGE_LANE = 13,
        JUNCTION_LANE = 14,
        VIRTUAL_LANE = 15,
        REFEE_PATH = 16,
        TRAJ_HISTORY = 17,
        AGENT_CURRENT = 18,
        AGENT_PAST = 19,
        AGENT_CAR = 20,
        CROSSWALK_EDGE = 21,
        STATIC_OBJ = 22,
        //REFERENCE_LINE = 23,
        PLAN_CANDIDATE = 23,
        PLAN_BEST = 24,
        REF_FREE = 25,
    };
    // 地图元素样式
    const std::vector<MapElementStyle> map_styles = {
            {"lane left/right", white_color, 1, white_color, 2, false},
            {"lane center", dark_gray_color, 2, dark_gray_color, 1, false},
            {"boundary", red_color, 1, cyan_color, 2, false},
            {"intersection", light_yellow_color, 1, cyan_color, 2, false},
            {"crosswalk", white_color, 1, cyan_color, 2, false},
            {"stop_line", red_color, 1, cyan_color, 2, false},
            {"entry region", green_color * 0.85, 1, cyan_color, 2},
            {"traffic light", red_color, 1, cyan_color, 2, false},
            {"traffic sign", white_color, 1, cyan_color, 2, true},
            {"route", yellow_color, 1, yellow_color, 2, false},
            {"route elem.", cyan_color, 2, cyan_color, 2, false},
            {"intersec elem.", dark_yellow, 1, dark_yellow, 2, false},
            {"ego.", red_color, 1, red_color, 2, false},
            {"merged lane.", light_yellow_color, 1, dark_yellow * 0.8, 2, false},
            {"junction lane", light_yellow_color, 1, dark_yellow * 0.8, 2, false},
            {"virtual lane", light_yellow_color, 1, dark_yellow * 0.8, 2, false},
            {"refee path", magenta_color, 1, magenta_color, 2, false},
            {"traj. history", traj_history_color, 1, traj_history_color, 2, false},
            {"agent current", agent_current_color, 1, agent_current_color, 2, false},
            {"agent past", raspberry_color, 2, raspberry_color, 2, false},
            {"agent car", agent_box_color, 2, agent_box_color, 2, false},
            {"crosswalk edge", llight_green_color, 1, cyan_color, 2, false},
            {"static obj", red_color, 1, red_color, 2, false},
            //{"reference line", med_light_yellow_color, 3, dark_yellow * 0.8, 2, false},
            {"plan candidate", plan_candidate, 2, plan_candidate, 2, false},
            {"plan best", plan_best, 2, plan_best, 2, false},
            {"plan ref_free", ref_free, 2, ref_free, 2, false},
    };
} // namespace vz

#endif // UTILS_VIZ_COLOR_HPP
