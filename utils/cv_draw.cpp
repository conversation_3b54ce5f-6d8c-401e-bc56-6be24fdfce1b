#include <opencv2/opencv.hpp>
#include "cv_draw.hpp"

void drawArrow(cv::Mat& img, cv::Point2f pStart, cv::Point2f pEnd, cv::Scalar color, int thickness, int lineType, int len, int alpha)
{
    cv::Point arrow;
    //计算 θ 角（最简单的一种情况在下面图示中已经展示，关键在于 atan2 函数，详情见下面）
    float angle = atan2((double)(pStart.y - pEnd.y), (double)(pStart.x - pEnd.x));
    cv::line(img, pStart, pEnd, color, thickness, lineType);
    //计算箭角边的另一端的端点位置（上面的还是下面的要看箭头的指向，也就是pStart和pEnd的位置）
    arrow.x = pEnd.x + len * cos(angle + CV_PI * alpha / 180);
    arrow.y = pEnd.y + len * sin(angle + CV_PI * alpha / 180);
    cv::line(img, pEnd, arrow, color, thickness, lineType);
    arrow.x = pEnd.x + len * cos(angle - CV_PI * alpha / 180);
    arrow.y = pEnd.y + len * sin(angle - CV_PI * alpha / 180);
    cv::line(img, pEnd, arrow, color, thickness, lineType);
}

void DrawDashedLine(cv::Mat& img, cv::Point pt1, cv::Point pt2, cv::Scalar color, int thickness, std::string style, int gap) {
    float dx = pt1.x - pt2.x;
    float dy = pt1.y - pt2.y;
    float dist = std::hypot(dx, dy);

    std::vector<cv::Point> pts;
    for (int i = 0; i < dist; i += gap) {
        float r = static_cast<float>(i / dist);
        int x = static_cast<int>((pt1.x * (1.0 - r) + pt2.x * r) + .5);
        int y = static_cast<int>((pt1.y * (1.0 - r) + pt2.y * r) + .5);
        pts.emplace_back(x, y);
    }

    int pts_size = pts.size();

    if (style == "dotted") {
        for (int i = 0; i < pts_size; ++i) {
            cv::circle(img, pts[i], thickness, color, -1);
        }
    }else{
        cv::Point s = pts[0];
        cv::Point e = pts[0];

        for (int i = 0; i < pts_size; ++i) {
            s = e;
            e = pts[i];
            if (i % 2 == 1) {
                cv::line(img, s, e, color, thickness);
            }
        }
    }
}
