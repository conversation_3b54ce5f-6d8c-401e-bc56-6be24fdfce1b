#ifndef CV_DRAW_HPP
#define CV_DRAW_HPP

#include <opencv2/opencv.hpp>

void drawArrow(cv::Mat& img, cv::Point2f pStart, cv::Point2f pEnd, cv::Scalar color = cv::Scalar(0, 255, 0), int thickness = 1, int lineType = 8, int len=10, int alpha=30);

void DrawDashedLine(cv::Mat& img, cv::Point pt1, cv::Point pt2, cv::Scalar color, int thickness = 1, std::string style = "dotted", int gap = 10);

#endif // CV_DRAW_HPP
